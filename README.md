# WnBios POC DLL - 内存读写驱动库

这是一个基于eneio64驱动的Windows内存读写DLL库，支持各种数据类型的读取和写入操作。

## 功能特性

### 支持的数据类型
- **基础类型**: BYTE, WORD, DWORD, QWORD
- **浮点类型**: FLOAT, DOUBLE  
- **字符串类型**: ASCII字符串, Unicode宽字符串
- **字节数组**: 任意长度的字节数据

### 主要功能
- 进程附加和信息获取
- 内存读写操作（支持多种数据类型）
- 模块枚举和基址查找
- 虚拟地址到物理地址转换
- 地址有效性检查
- 错误信息获取

## 编译说明

### 环境要求
- Visual Studio 2022 (v143工具集)
- Windows 10/11 SDK
- 管理员权限（驱动需要）

### 编译步骤
1. 打开 `wnbios_poc.sln` 解决方案文件
2. 选择 `Release|x64` 配置
3. 生成解决方案
4. 输出文件：`wnbios_poc.dll`

## 使用方法

### 1. 基本初始化

```cpp
#include "dll_exports.h"

// 加载DLL
HMODULE hDll = LoadLibrary(L"wnbios_poc.dll");

// 获取函数指针
InitializeDriverFunc InitializeDriver = (InitializeDriverFunc)GetProcAddress(hDll, "InitializeDriver");
CleanupDriverFunc CleanupDriver = (CleanupDriverFunc)GetProcAddress(hDll, "CleanupDriver");

// 初始化驱动
if (!InitializeDriver()) {
    printf("Failed to initialize driver\n");
    return;
}
```

### 2. 进程操作

```cpp
// 获取进程信息
ProcessInfo proc_info;
if (GetProcessInfo("notepad.exe", &proc_info)) {
    printf("Process PID: %u\n", proc_info.process_id);
    printf("Base Address: 0x%llx\n", proc_info.base_address);
}

// 附加到进程
if (AttachToProcess("notepad.exe")) {
    printf("Successfully attached to process\n");
}
```

### 3. 内存读取操作

```cpp
// 读取不同数据类型
uint8_t byte_val;
uint16_t word_val;
uint32_t dword_val;
uint64_t qword_val;
float float_val;
double double_val;

ReadMemoryByte(address, &byte_val);
ReadMemoryWord(address, &word_val);
ReadMemoryDword(address, &dword_val);
ReadMemoryQword(address, &qword_val);
ReadMemoryFloat(address, &float_val);
ReadMemoryDouble(address, &double_val);

// 读取字符串
char string_buffer[256];
ReadMemoryString(address, string_buffer, sizeof(string_buffer));

// 读取字节数组
uint8_t buffer[1024];
ReadMemoryBytes(address, buffer, sizeof(buffer));
```

### 4. 内存写入操作

```cpp
// 写入不同数据类型
WriteMemoryByte(address, 0x90);
WriteMemoryDword(address, 0x12345678);
WriteMemoryFloat(address, 3.14f);

// 写入字符串
WriteMemoryString(address, "Hello World");

// 写入字节数组
uint8_t data[] = {0x90, 0x90, 0x90, 0x90};
WriteMemoryBytes(address, data, sizeof(data));
```

### 5. 模块操作

```cpp
// 查找模块基址
uint64_t kernel32_base = FindModuleBase("notepad.exe", L"kernel32.dll");
uint64_t ntdll_base = FindModuleBase("notepad.exe", L"ntdll.dll");

printf("kernel32.dll base: 0x%llx\n", kernel32_base);
printf("ntdll.dll base: 0x%llx\n", ntdll_base);
```

### 6. 地址转换和验证

```cpp
// 虚拟地址转物理地址
uint64_t physical_addr = VirtualToPhysical(virtual_addr);

// 检查地址有效性
if (IsValidAddress(address)) {
    printf("Address is valid\n");
}
```

### 7. 错误处理

```cpp
char error_buffer[256];
if (GetLastDriverError(error_buffer, sizeof(error_buffer))) {
    printf("Last error: %s\n", error_buffer);
}
```

### 8. 清理资源

```cpp
// 清理驱动资源
CleanupDriver();

// 卸载DLL
FreeLibrary(hDll);
```

## API参考

### 初始化函数
- `InitializeDriver()` - 初始化驱动
- `CleanupDriver()` - 清理驱动资源

### 进程函数
- `GetProcessInfo()` - 获取进程信息
- `AttachToProcess()` - 附加到进程
- `GetAttachedProcessId()` - 获取已附加的进程ID

### 内存读取函数
- `ReadMemoryByte/Word/Dword/Qword()` - 读取基础数据类型
- `ReadMemoryFloat/Double()` - 读取浮点类型
- `ReadMemoryString/WString()` - 读取字符串
- `ReadMemoryBytes()` - 读取字节数组

### 内存写入函数
- `WriteMemoryByte/Word/Dword/Qword()` - 写入基础数据类型
- `WriteMemoryFloat/Double()` - 写入浮点类型
- `WriteMemoryString/WString()` - 写入字符串
- `WriteMemoryBytes()` - 写入字节数组

### 模块函数
- `FindModuleBase()` - 查找模块基址

### 实用函数
- `VirtualToPhysical()` - 地址转换
- `IsValidAddress()` - 地址验证
- `GetLastDriverError()` - 获取错误信息

## 注意事项

1. **管理员权限**: 此DLL需要管理员权限运行，因为它需要加载内核驱动
2. **目标进程**: 确保目标进程正在运行且可访问
3. **内存保护**: 某些受保护的进程可能无法访问
4. **错误处理**: 始终检查函数返回值并处理错误
5. **资源清理**: 使用完毕后务必调用CleanupDriver()

## 示例程序

参考 `dll_test_example.cpp` 文件查看完整的使用示例。

## 免责声明

此工具仅用于安全研究和教育目的。使用者需要确保遵守当地法律法规，不得用于非法用途。
