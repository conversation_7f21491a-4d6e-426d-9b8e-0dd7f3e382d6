#!/usr/bin/env python3
"""
简化版Python示例：基本内存读写操作
专注于核心功能，不包含模块枚举
需要以管理员权限运行
"""

import ctypes
from ctypes import wintypes, Structure, c_uint8, c_uint16, c_uint32, c_uint64, c_float, c_double, c_char
import sys

# 进程信息结构
class ProcessInfo(Structure):
    _fields_ = [
        ("process_id", c_uint32),
        ("process_name", c_char * 256),
        ("base_address", c_uint64),
        ("cr3", c_uint64)
    ]

class SimpleMemoryDriver:
    def __init__(self, dll_path=None):
        """初始化内存驱动 - 简化版"""
        # 尝试多个可能的DLL路径
        possible_paths = [
            "wnbios_poc.dll",
            "x64\\Debug\\wnbios_poc.dll",
            "x64\\Release\\wnbios_poc.dll",
            "wnbios_poc\\x64\\Debug\\wnbios_poc.dll",
            "wnbios_poc\\x64\\Release\\wnbios_poc.dll",
            "Debug\\wnbios_poc.dll",
            "Release\\wnbios_poc.dll"
        ]

        if dll_path:
            possible_paths.insert(0, dll_path)

        dll_loaded = False
        for path in possible_paths:
            try:
                print(f"[*] Trying to load DLL from: {path}")
                self.dll = ctypes.CDLL(path)
                print(f"[+] Successfully loaded DLL from: {path}")
                dll_loaded = True
                break
            except Exception as e:
                print(f"[-] Failed to load from {path}: {e}")
                continue

        if not dll_loaded:
            raise Exception("Could not find wnbios_poc.dll in any expected location")

        try:
            self._setup_basic_functions()

            # 初始化驱动
            if not self.dll.InitializeDriver():
                error_msg = self.get_last_error()
                raise Exception(f"Failed to initialize driver: {error_msg}")

            print("[+] Driver initialized successfully!")

        except Exception as e:
            print(f"[-] Failed to initialize driver: {e}")
            raise

    def _setup_basic_functions(self):
        """设置基本函数原型"""
        # 初始化函数
        self.dll.InitializeDriver.restype = wintypes.BOOL
        self.dll.CleanupDriver.restype = None
        
        # 进程函数
        self.dll.GetProcessInfo.argtypes = [ctypes.c_char_p, ctypes.POINTER(ProcessInfo)]
        self.dll.GetProcessInfo.restype = wintypes.BOOL
        
        # 基本内存读取函数
        self.dll.ReadMemoryByte.argtypes = [c_uint64, ctypes.POINTER(c_uint8)]
        self.dll.ReadMemoryByte.restype = wintypes.BOOL
        
        self.dll.ReadMemoryWord.argtypes = [c_uint64, ctypes.POINTER(c_uint16)]
        self.dll.ReadMemoryWord.restype = wintypes.BOOL
        
        self.dll.ReadMemoryDword.argtypes = [c_uint64, ctypes.POINTER(c_uint32)]
        self.dll.ReadMemoryDword.restype = wintypes.BOOL
        
        self.dll.ReadMemoryQword.argtypes = [c_uint64, ctypes.POINTER(c_uint64)]
        self.dll.ReadMemoryQword.restype = wintypes.BOOL
        
        self.dll.ReadMemoryFloat.argtypes = [c_uint64, ctypes.POINTER(c_float)]
        self.dll.ReadMemoryFloat.restype = wintypes.BOOL
        
        self.dll.ReadMemoryDouble.argtypes = [c_uint64, ctypes.POINTER(c_double)]
        self.dll.ReadMemoryDouble.restype = wintypes.BOOL
        
        self.dll.ReadMemoryBytes.argtypes = [c_uint64, ctypes.POINTER(c_uint8), c_uint32]
        self.dll.ReadMemoryBytes.restype = wintypes.BOOL
        
        # 基本内存写入函数
        self.dll.WriteMemoryByte.argtypes = [c_uint64, c_uint8]
        self.dll.WriteMemoryByte.restype = wintypes.BOOL
        
        self.dll.WriteMemoryDword.argtypes = [c_uint64, c_uint32]
        self.dll.WriteMemoryDword.restype = wintypes.BOOL
        
        self.dll.WriteMemoryFloat.argtypes = [c_uint64, c_float]
        self.dll.WriteMemoryFloat.restype = wintypes.BOOL
        
        # 实用函数
        self.dll.IsValidAddress.argtypes = [c_uint64]
        self.dll.IsValidAddress.restype = wintypes.BOOL
        
        self.dll.GetLastDriverError.argtypes = [ctypes.c_char_p, c_uint32]
        self.dll.GetLastDriverError.restype = wintypes.BOOL

    def get_last_error(self):
        """获取最后的错误信息"""
        error_buffer = ctypes.create_string_buffer(256)
        if self.dll.GetLastDriverError(error_buffer, 256):
            return error_buffer.value.decode('utf-8')
        return "Unknown error"

    def get_process_info(self, process_name):
        """获取进程信息"""
        proc_info = ProcessInfo()
        if self.dll.GetProcessInfo(process_name.encode('utf-8'), ctypes.byref(proc_info)):
            return {
                'pid': proc_info.process_id,
                'name': proc_info.process_name.decode('utf-8'),
                'base_address': proc_info.base_address,
                'cr3': proc_info.cr3
            }
        return None

    def read_byte(self, address):
        """读取字节"""
        value = c_uint8()
        if self.dll.ReadMemoryByte(address, ctypes.byref(value)):
            return value.value
        return None

    def read_word(self, address):
        """读取字"""
        value = c_uint16()
        if self.dll.ReadMemoryWord(address, ctypes.byref(value)):
            return value.value
        return None

    def read_dword(self, address):
        """读取双字"""
        value = c_uint32()
        if self.dll.ReadMemoryDword(address, ctypes.byref(value)):
            return value.value
        return None

    def read_qword(self, address):
        """读取四字"""
        value = c_uint64()
        if self.dll.ReadMemoryQword(address, ctypes.byref(value)):
            return value.value
        return None

    def read_float(self, address):
        """读取浮点数"""
        value = c_float()
        if self.dll.ReadMemoryFloat(address, ctypes.byref(value)):
            return value.value
        return None

    def read_double(self, address):
        """读取双精度浮点数"""
        value = c_double()
        if self.dll.ReadMemoryDouble(address, ctypes.byref(value)):
            return value.value
        return None

    def read_bytes(self, address, size):
        """读取字节数组"""
        buffer = (c_uint8 * size)()
        if self.dll.ReadMemoryBytes(address, buffer, size):
            return bytes(buffer)
        return None

    def write_byte(self, address, value):
        """写入字节"""
        return bool(self.dll.WriteMemoryByte(address, value))

    def write_dword(self, address, value):
        """写入双字"""
        return bool(self.dll.WriteMemoryDword(address, value))

    def write_float(self, address, value):
        """写入浮点数"""
        return bool(self.dll.WriteMemoryFloat(address, value))

    def is_valid_address(self, address):
        """检查地址是否有效"""
        return bool(self.dll.IsValidAddress(address))

    def cleanup(self):
        """清理资源"""
        self.dll.CleanupDriver()

def main():
    """主函数 - 简化版测试"""
    print("[*] Simple Python WnBios POC DLL Test")
    print("[*] Make sure to run as Administrator!")
    print("[*] This version focuses on basic memory operations only")
    
    try:
        # 初始化驱动
        driver = SimpleMemoryDriver()
        
        # 测试进程信息获取
        target_process = "notepad.exe"
        print(f"\n[*] Getting process info for {target_process}...")
        
        proc_info = driver.get_process_info(target_process)
        if proc_info:
            print(f"[+] Process found:")
            print(f"    PID: {proc_info['pid']}")
            print(f"    Name: {proc_info['name']}")
            print(f"    Base: 0x{proc_info['base_address']:016X}")
            print(f"    CR3: 0x{proc_info['cr3']:016X}")
            
            # 测试内存读取
            print(f"\n[*] Testing basic memory reads...")
            base_addr = proc_info['base_address']
            
            # 检查地址有效性
            if driver.is_valid_address(base_addr):
                print(f"[+] Base address is valid")
                
                # 读取PE头
                pe_header = driver.read_bytes(base_addr, 16)
                if pe_header:
                    print(f"[+] PE Header: {pe_header.hex().upper()}")
                    if pe_header[0:2] == b'MZ':
                        print(f"[+] Valid PE signature found!")
                
                # 读取不同数据类型
                print(f"\n[*] Testing different data type reads...")
                
                byte_val = driver.read_byte(base_addr)
                if byte_val is not None:
                    print(f"[+] Byte at base: 0x{byte_val:02X}")
                
                word_val = driver.read_word(base_addr)
                if word_val is not None:
                    print(f"[+] Word at base: 0x{word_val:04X}")
                
                dword_val = driver.read_dword(base_addr)
                if dword_val is not None:
                    print(f"[+] Dword at base: 0x{dword_val:08X}")
                
                qword_val = driver.read_qword(base_addr)
                if qword_val is not None:
                    print(f"[+] Qword at base: 0x{qword_val:016X}")
                
                # 测试内存写入（谨慎操作）
                print(f"\n[*] Testing memory write (reading back original value first)...")
                test_addr = base_addr + 0x1000  # 偏移一些位置避免破坏PE头
                
                if driver.is_valid_address(test_addr):
                    original_byte = driver.read_byte(test_addr)
                    if original_byte is not None:
                        print(f"[+] Original byte at 0x{test_addr:016X}: 0x{original_byte:02X}")
                        
                        # 写入测试值
                        test_value = 0x90
                        if driver.write_byte(test_addr, test_value):
                            print(f"[+] Successfully wrote 0x{test_value:02X}")
                            
                            # 读回验证
                            read_back = driver.read_byte(test_addr)
                            if read_back == test_value:
                                print(f"[+] Write verified: 0x{read_back:02X}")
                                
                                # 恢复原值
                                if driver.write_byte(test_addr, original_byte):
                                    print(f"[+] Original value restored")
                            else:
                                print(f"[-] Write verification failed")
                        else:
                            print(f"[-] Failed to write test value")
                else:
                    print(f"[-] Test address is not valid")
            else:
                print(f"[-] Base address is not valid")
                
        else:
            error_msg = driver.get_last_error()
            print(f"[-] Failed to get process info: {error_msg}")
            print(f"[-] Make sure {target_process} is running")
        
    except Exception as e:
        print(f"[-] Error: {e}")
    
    finally:
        # 清理资源
        print(f"\n[*] Cleaning up...")
        if 'driver' in locals():
            driver.cleanup()
        print(f"[+] Test completed!")

if __name__ == "__main__":
    main()
