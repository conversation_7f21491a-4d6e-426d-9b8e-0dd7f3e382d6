#!/usr/bin/env python3
"""
检查DLL文件和依赖项
"""

import os
import sys
import ctypes
from pathlib import Path

def check_dll_exists():
    """检查DLL文件是否存在"""
    possible_paths = [
        "wnbios_poc.dll",
        "x64\\Debug\\wnbios_poc.dll",
        "x64\\Release\\wnbios_poc.dll", 
        "wnbios_poc\\x64\\Debug\\wnbios_poc.dll",
        "wnbios_poc\\x64\\Release\\wnbios_poc.dll",
        "Debug\\wnbios_poc.dll",
        "Release\\wnbios_poc.dll"
    ]
    
    print("[*] Checking for DLL files...")
    found_dlls = []
    
    for path in possible_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"[+] Found: {path} ({size} bytes)")
            found_dlls.append(path)
        else:
            print(f"[-] Not found: {path}")
    
    return found_dlls

def check_dll_dependencies(dll_path):
    """检查DLL依赖项"""
    print(f"\n[*] Checking dependencies for: {dll_path}")
    
    try:
        # 尝试加载DLL
        dll = ctypes.CDLL(dll_path)
        print(f"[+] Successfully loaded DLL")
        
        # 检查是否有InitializeDriver函数
        try:
            init_func = dll.InitializeDriver
            print(f"[+] Found InitializeDriver function")
        except AttributeError:
            print(f"[-] InitializeDriver function not found")
        
        return True
        
    except Exception as e:
        print(f"[-] Failed to load DLL: {e}")
        return False

def check_admin_rights():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    print("[*] DLL Checker Tool")
    print("=" * 50)
    
    # 检查管理员权限
    if check_admin_rights():
        print("[+] Running with Administrator privileges")
    else:
        print("[!] WARNING: Not running as Administrator")
        print("[!] The driver may fail to initialize without admin rights")
    
    print()
    
    # 检查DLL文件
    found_dlls = check_dll_exists()
    
    if not found_dlls:
        print("\n[-] No DLL files found!")
        print("[*] Make sure you have compiled the project successfully")
        print("[*] Expected locations:")
        print("    - x64\\Debug\\wnbios_poc.dll (Debug build)")
        print("    - x64\\Release\\wnbios_poc.dll (Release build)")
        return
    
    # 测试每个找到的DLL
    print(f"\n[*] Testing DLL loading...")
    working_dlls = []
    
    for dll_path in found_dlls:
        if check_dll_dependencies(dll_path):
            working_dlls.append(dll_path)
    
    if working_dlls:
        print(f"\n[+] Working DLL files:")
        for dll in working_dlls:
            print(f"    - {dll}")
        
        print(f"\n[*] Recommended usage:")
        print(f"    python simple_python_example.py")
        print(f"    (The script will automatically find the DLL)")
        
    else:
        print(f"\n[-] No working DLL files found!")
        print(f"[*] This could be due to:")
        print(f"    - Missing dependencies (Visual C++ Redistributable)")
        print(f"    - Incorrect compilation")
        print(f"    - Missing driver files")

if __name__ == "__main__":
    main()
