@echo off
echo Building WnBios POC DLL...

REM 设置Visual Studio环境
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

REM 编译DLL
echo Compiling DLL...
msbuild wnbios_poc.sln /p:Configuration=Release /p:Platform=x64

if %ERRORLEVEL% EQU 0 (
    echo.
    echo [+] Build successful!
    echo [+] Output: wnbios_poc\x64\Release\wnbios_poc.dll
    echo.
    echo To test the DLL, compile and run dll_test_example.cpp
    echo Make sure to run as Administrator!
) else (
    echo.
    echo [-] Build failed!
    echo Check the error messages above.
)

pause
