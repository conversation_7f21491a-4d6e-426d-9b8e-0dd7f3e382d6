@echo off
echo Building WnBios POC DLL...

REM 设置Visual Studio环境
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

REM 清理之前的编译
echo Cleaning previous build...
msbuild wnbios_poc.sln /p:Configuration=Debug /p:Platform=x64 /t:Clean
msbuild wnbios_poc.sln /p:Configuration=Release /p:Platform=x64 /t:Clean

REM 编译Debug版本
echo Compiling Debug DLL...
msbuild wnbios_poc.sln /p:Configuration=Debug /p:Platform=x64

if %ERRORLEVEL% EQU 0 (
    echo.
    echo [+] Debug build successful!
    echo [+] Output: x64\Debug\wnbios_poc.dll

    REM 编译Release版本
    echo.
    echo Compiling Release DLL...
    msbuild wnbios_poc.sln /p:Configuration=Release /p:Platform=x64

    if %ERRORLEVEL% EQU 0 (
        echo.
        echo [+] Release build successful!
        echo [+] Output: x64\Release\wnbios_poc.dll
        echo.
        echo [*] Testing the fixed DLL...
        echo [*] Running debug test...
        python debug_process_test.py
    ) else (
        echo.
        echo [-] Release build failed!
    )
) else (
    echo.
    echo [-] Debug build failed!
    echo Check the error messages above.
)

pause
