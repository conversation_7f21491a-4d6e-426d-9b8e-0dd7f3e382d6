#pragma once

#ifdef BUILDING_DLL
#define DLL_EXPORT __declspec(dllexport)
#else
#define DLL_EXPORT __declspec(dllimport)
#endif

#include <windows.h>
#include <stdint.h>

// 数据类型枚举
typedef enum {
    DATA_TYPE_BYTE = 1,
    DATA_TYPE_WORD = 2,
    DATA_TYPE_DWORD = 4,
    DATA_TYPE_QWORD = 8,
    DATA_TYPE_FLOAT = 10,
    DATA_TYPE_DOUBLE = 11,
    DATA_TYPE_STRING = 20,
    DATA_TYPE_WSTRING = 21,
    DATA_TYPE_BYTES = 30
} DataType;

// 数据结构
typedef struct {
    DataType type;
    union {
        uint8_t byte_val;
        uint16_t word_val;
        uint32_t dword_val;
        uint64_t qword_val;
        float float_val;
        double double_val;
        char* string_val;
        wchar_t* wstring_val;
        struct {
            uint8_t* data;
            size_t size;
        } bytes_val;
    };
} MemoryData;

// 进程信息结构
typedef struct {
    uint32_t process_id;
    char process_name[256];
    uint64_t base_address;
    uint64_t cr3;
} ProcessInfo;

// 模块信息结构
typedef struct {
    wchar_t module_name[256];
    uint64_t base_address;
    uint32_t size;
} ModuleInfo;

#ifdef __cplusplus
extern "C" {
#endif

// 初始化和清理函数
DLL_EXPORT BOOL InitializeDriver();
DLL_EXPORT void CleanupDriver();

// 进程相关函数
DLL_EXPORT BOOL GetProcessInfo(const char* process_name, ProcessInfo* info);
DLL_EXPORT BOOL AttachToProcess(const char* process_name);
DLL_EXPORT uint32_t GetAttachedProcessId();
DLL_EXPORT BOOL ListRunningProcesses(ProcessInfo* processes, uint32_t* count, uint32_t max_count);
DLL_EXPORT BOOL SearchProcessesByKeyword(const char* keyword, ProcessInfo* processes, uint32_t* count, uint32_t max_count);

// 内存读取函数 - 基础类型
DLL_EXPORT BOOL ReadMemoryByte(uint64_t address, uint8_t* value);
DLL_EXPORT BOOL ReadMemoryWord(uint64_t address, uint16_t* value);
DLL_EXPORT BOOL ReadMemoryDword(uint64_t address, uint32_t* value);
DLL_EXPORT BOOL ReadMemoryQword(uint64_t address, uint64_t* value);
DLL_EXPORT BOOL ReadMemoryFloat(uint64_t address, float* value);
DLL_EXPORT BOOL ReadMemoryDouble(uint64_t address, double* value);

// 内存读取函数 - 字符串和字节数组
DLL_EXPORT BOOL ReadMemoryString(uint64_t address, char* buffer, uint32_t buffer_size);
DLL_EXPORT BOOL ReadMemoryWString(uint64_t address, wchar_t* buffer, uint32_t buffer_size);
DLL_EXPORT BOOL ReadMemoryBytes(uint64_t address, uint8_t* buffer, uint32_t size);

// 内存写入函数 - 基础类型
DLL_EXPORT BOOL WriteMemoryByte(uint64_t address, uint8_t value);
DLL_EXPORT BOOL WriteMemoryWord(uint64_t address, uint16_t value);
DLL_EXPORT BOOL WriteMemoryDword(uint64_t address, uint32_t value);
DLL_EXPORT BOOL WriteMemoryQword(uint64_t address, uint64_t value);
DLL_EXPORT BOOL WriteMemoryFloat(uint64_t address, float value);
DLL_EXPORT BOOL WriteMemoryDouble(uint64_t address, double value);

// 内存写入函数 - 字符串和字节数组
DLL_EXPORT BOOL WriteMemoryString(uint64_t address, const char* value);
DLL_EXPORT BOOL WriteMemoryWString(uint64_t address, const wchar_t* value);
DLL_EXPORT BOOL WriteMemoryBytes(uint64_t address, const uint8_t* buffer, uint32_t size);

// 通用内存读写函数
DLL_EXPORT BOOL ReadMemoryGeneric(uint64_t address, MemoryData* data);
DLL_EXPORT BOOL WriteMemoryGeneric(uint64_t address, const MemoryData* data);

// 模块相关函数
DLL_EXPORT BOOL EnumerateProcessModules(const char* process_name, ModuleInfo* modules, uint32_t* count, uint32_t max_count);
DLL_EXPORT uint64_t FindModuleBase(const char* process_name, const wchar_t* module_name);

// 地址转换函数
DLL_EXPORT uint64_t VirtualToPhysical(uint64_t virtual_address);

// 实用函数
DLL_EXPORT BOOL IsValidAddress(uint64_t address);
DLL_EXPORT BOOL GetLastDriverError(char* error_buffer, uint32_t buffer_size);

#ifdef __cplusplus
}
#endif
