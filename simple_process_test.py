#!/usr/bin/env python3
"""
简单的进程测试 - 直接测试Windows API
"""

import ctypes
from ctypes import wintypes
import subprocess
import time

def test_windows_api_directly():
    """直接测试Windows API进程枚举"""
    
    # 定义结构
    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", wintypes.DWORD),
            ("cntUsage", wintypes.DWORD),
            ("th32ProcessID", wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(wintypes.ULONG)),
            ("th32ModuleID", wintypes.DWORD),
            ("cntThreads", wintypes.DWORD),
            ("th32ParentProcessID", wintypes.DWORD),
            ("pcPriClassBase", wintypes.LONG),
            ("dwFlags", wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]
    
    # 常量
    TH32CS_SNAPPROCESS = 0x00000002
    INVALID_HANDLE_VALUE = -1
    
    # 函数
    kernel32 = ctypes.windll.kernel32
    
    # CreateToolhelp32Snapshot
    kernel32.CreateToolhelp32Snapshot.argtypes = [wintypes.DWORD, wintypes.DWORD]
    kernel32.CreateToolhelp32Snapshot.restype = wintypes.HANDLE
    
    # Process32First
    kernel32.Process32First.argtypes = [wintypes.HANDLE, ctypes.POINTER(PROCESSENTRY32)]
    kernel32.Process32First.restype = wintypes.BOOL
    
    # Process32Next
    kernel32.Process32Next.argtypes = [wintypes.HANDLE, ctypes.POINTER(PROCESSENTRY32)]
    kernel32.Process32Next.restype = wintypes.BOOL
    
    # CloseHandle
    kernel32.CloseHandle.argtypes = [wintypes.HANDLE]
    kernel32.CloseHandle.restype = wintypes.BOOL
    
    print("[*] Testing Windows API process enumeration...")
    
    try:
        # 创建快照
        hSnapshot = kernel32.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
        if hSnapshot == INVALID_HANDLE_VALUE:
            print("[-] Failed to create process snapshot")
            return False
        
        pe32 = PROCESSENTRY32()
        pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)
        
        notepad_found = False
        process_count = 0
        
        # 获取第一个进程
        if kernel32.Process32First(hSnapshot, ctypes.byref(pe32)):
            process_count += 1
            try:
                name = pe32.szExeFile.decode('utf-8', errors='ignore')
                if 'notepad' in name.lower():
                    print(f"[+] Found notepad: PID={pe32.th32ProcessID}, Name={name}")
                    notepad_found = True
            except:
                pass
            
            # 获取其余进程
            while kernel32.Process32Next(hSnapshot, ctypes.byref(pe32)):
                process_count += 1
                try:
                    name = pe32.szExeFile.decode('utf-8', errors='ignore')
                    if 'notepad' in name.lower():
                        print(f"[+] Found notepad: PID={pe32.th32ProcessID}, Name={name}")
                        notepad_found = True
                except:
                    pass
        
        kernel32.CloseHandle(hSnapshot)
        print(f"[+] Total processes enumerated: {process_count}")
        return notepad_found
        
    except Exception as e:
        print(f"[-] Error: {e}")
        return False

def find_process_id(process_name):
    """查找进程ID - 模拟DLL中的逻辑"""
    
    # 定义结构
    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", wintypes.DWORD),
            ("cntUsage", wintypes.DWORD),
            ("th32ProcessID", wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(wintypes.ULONG)),
            ("th32ModuleID", wintypes.DWORD),
            ("cntThreads", wintypes.DWORD),
            ("th32ParentProcessID", wintypes.DWORD),
            ("pcPriClassBase", wintypes.LONG),
            ("dwFlags", wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]
    
    # 常量
    TH32CS_SNAPPROCESS = 0x00000002
    INVALID_HANDLE_VALUE = -1
    
    # 函数
    kernel32 = ctypes.windll.kernel32
    
    kernel32.CreateToolhelp32Snapshot.argtypes = [wintypes.DWORD, wintypes.DWORD]
    kernel32.CreateToolhelp32Snapshot.restype = wintypes.HANDLE
    
    kernel32.Process32First.argtypes = [wintypes.HANDLE, ctypes.POINTER(PROCESSENTRY32)]
    kernel32.Process32First.restype = wintypes.BOOL
    
    kernel32.Process32Next.argtypes = [wintypes.HANDLE, ctypes.POINTER(PROCESSENTRY32)]
    kernel32.Process32Next.restype = wintypes.BOOL
    
    kernel32.CloseHandle.argtypes = [wintypes.HANDLE]
    kernel32.CloseHandle.restype = wintypes.BOOL
    
    try:
        hSnapshot = kernel32.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
        if hSnapshot == INVALID_HANDLE_VALUE:
            return 0
        
        pe32 = PROCESSENTRY32()
        pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)
        
        # 获取第一个进程
        if kernel32.Process32First(hSnapshot, ctypes.byref(pe32)):
            try:
                name = pe32.szExeFile.decode('utf-8', errors='ignore')
                if name == process_name:
                    pid = pe32.th32ProcessID
                    kernel32.CloseHandle(hSnapshot)
                    return pid
            except:
                pass
            
            # 获取其余进程
            while kernel32.Process32Next(hSnapshot, ctypes.byref(pe32)):
                try:
                    name = pe32.szExeFile.decode('utf-8', errors='ignore')
                    if name == process_name:
                        pid = pe32.th32ProcessID
                        kernel32.CloseHandle(hSnapshot)
                        return pid
                except:
                    pass
        
        kernel32.CloseHandle(hSnapshot)
        return 0
        
    except Exception as e:
        print(f"[-] Error finding process: {e}")
        return 0

def main():
    print("[*] Simple Process Test")
    print("=" * 40)
    
    # 测试基本的进程枚举
    print("[1] Testing basic process enumeration...")
    notepad_found = test_windows_api_directly()
    
    if not notepad_found:
        print("\n[2] Starting notepad...")
        try:
            proc = subprocess.Popen(["notepad.exe"])
            print(f"[+] Started notepad.exe (PID: {proc.pid})")
            time.sleep(2)
            
            print("\n[3] Re-testing process enumeration...")
            notepad_found = test_windows_api_directly()
        except Exception as e:
            print(f"[-] Failed to start notepad: {e}")
    
    if notepad_found:
        print("\n[4] Testing process ID lookup...")
        pid = find_process_id("notepad.exe")
        if pid:
            print(f"[+] Found notepad.exe with PID: {pid}")
        else:
            print("[-] Failed to find notepad.exe PID")
    
    print("\n[+] Test completed!")

if __name__ == "__main__":
    main()
