﻿  drv.cpp
  dll_implementation.cpp
C:\Users\<USER>\Desktop\驱动漏洞\dlltest\wnbios_poc\dll_implementation.cpp(73,22): warning C4244: “初始化”: 从“uintptr_t”转换到“uint32_t”，可能丢失数据
C:\Users\<USER>\Desktop\驱动漏洞\dlltest\wnbios_poc\dll_implementation.cpp(367,71): warning C4267: “参数”: 从“size_t”转换到“unsigned long”，可能丢失数据
C:\Users\<USER>\Desktop\驱动漏洞\dlltest\wnbios_poc\dll_implementation.cpp(384,71): warning C4267: “参数”: 从“size_t”转换到“unsigned long”，可能丢失数据
C:\Users\<USER>\Desktop\驱动漏洞\dlltest\wnbios_poc\dll_implementation.cpp(466,83): warning C4267: “参数”: 从“size_t”转换到“uint32_t”，可能丢失数据
  正在生成代码...
    正在创建库 C:\Users\<USER>\Desktop\驱动漏洞\dlltest\x64\Debug\wnbios_poc.lib 和对象 C:\Users\<USER>\Desktop\驱动漏洞\dlltest\x64\Debug\wnbios_poc.exp
  wnbios_poc.vcxproj -> C:\Users\<USER>\Desktop\驱动漏洞\dlltest\x64\Debug\wnbios_poc.dll
