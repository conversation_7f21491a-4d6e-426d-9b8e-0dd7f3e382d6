#!/usr/bin/env python3
"""
调试进程查找问题的测试脚本
"""

import ctypes
from ctypes import wintypes, Structure, c_uint8, c_uint16, c_uint32, c_uint64, c_char
import subprocess
import time
import sys

# 进程信息结构
class ProcessInfo(Structure):
    _fields_ = [
        ("process_id", c_uint32),
        ("process_name", c_char * 256),
        ("base_address", c_uint64),
        ("cr3", c_uint64)
    ]

def load_dll():
    """加载DLL"""
    possible_paths = [
        "x64\\Debug\\wnbios_poc.dll",
        "x64\\Release\\wnbios_poc.dll",
        "wnbios_poc.dll"
    ]
    
    for path in possible_paths:
        try:
            dll = ctypes.CDLL(path)
            print(f"[+] Loaded DLL from: {path}")
            return dll
        except Exception as e:
            continue
    
    raise Exception("Could not find DLL")

def setup_dll_functions(dll):
    """设置DLL函数"""
    # 初始化函数
    dll.InitializeDriver.restype = wintypes.BOOL
    dll.CleanupDriver.restype = None
    
    # 进程函数
    dll.GetProcessInfo.argtypes = [ctypes.c_char_p, ctypes.POINTER(ProcessInfo)]
    dll.GetProcessInfo.restype = wintypes.BOOL
    
    # 错误函数
    dll.GetLastDriverError.argtypes = [ctypes.c_char_p, c_uint32]
    dll.GetLastDriverError.restype = wintypes.BOOL

def get_last_error(dll):
    """获取错误信息"""
    error_buffer = ctypes.create_string_buffer(256)
    if dll.GetLastDriverError(error_buffer, 256):
        return error_buffer.value.decode('utf-8')
    return "Unknown error"

def list_processes_with_psutil():
    """使用psutil列出进程"""
    try:
        import psutil
        processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                processes.append((proc.info['pid'], proc.info['name']))
            except:
                continue
        return processes
    except ImportError:
        print("[!] psutil not available")
        return []

def list_processes_with_wmi():
    """使用WMI列出进程"""
    try:
        import wmi
        c = wmi.WMI()
        processes = []
        for process in c.Win32_Process():
            if process.Name and process.ProcessId:
                processes.append((process.ProcessId, process.Name))
        return processes
    except ImportError:
        print("[!] wmi not available")
        return []

def list_processes_with_ctypes():
    """使用ctypes直接调用Windows API列出进程"""
    import ctypes
    from ctypes import wintypes
    
    # 定义结构
    class PROCESSENTRY32(ctypes.Structure):
        _fields_ = [
            ("dwSize", wintypes.DWORD),
            ("cntUsage", wintypes.DWORD),
            ("th32ProcessID", wintypes.DWORD),
            ("th32DefaultHeapID", ctypes.POINTER(wintypes.ULONG)),
            ("th32ModuleID", wintypes.DWORD),
            ("cntThreads", wintypes.DWORD),
            ("th32ParentProcessID", wintypes.DWORD),
            ("pcPriClassBase", wintypes.LONG),
            ("dwFlags", wintypes.DWORD),
            ("szExeFile", ctypes.c_char * 260)
        ]
    
    # 常量
    TH32CS_SNAPPROCESS = 0x00000002
    INVALID_HANDLE_VALUE = -1
    
    # 函数
    kernel32 = ctypes.windll.kernel32
    
    # CreateToolhelp32Snapshot
    kernel32.CreateToolhelp32Snapshot.argtypes = [wintypes.DWORD, wintypes.DWORD]
    kernel32.CreateToolhelp32Snapshot.restype = wintypes.HANDLE
    
    # Process32First
    kernel32.Process32First.argtypes = [wintypes.HANDLE, ctypes.POINTER(PROCESSENTRY32)]
    kernel32.Process32First.restype = wintypes.BOOL
    
    # Process32Next
    kernel32.Process32Next.argtypes = [wintypes.HANDLE, ctypes.POINTER(PROCESSENTRY32)]
    kernel32.Process32Next.restype = wintypes.BOOL
    
    # CloseHandle
    kernel32.CloseHandle.argtypes = [wintypes.HANDLE]
    kernel32.CloseHandle.restype = wintypes.BOOL
    
    try:
        # 创建快照
        hSnapshot = kernel32.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
        if hSnapshot == INVALID_HANDLE_VALUE:
            return []
        
        processes = []
        pe32 = PROCESSENTRY32()
        pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)
        
        # 获取第一个进程
        if kernel32.Process32First(hSnapshot, ctypes.byref(pe32)):
            try:
                name = pe32.szExeFile.decode('utf-8', errors='ignore')
                processes.append((pe32.th32ProcessID, name))
            except:
                pass

            # 获取其余进程
            while kernel32.Process32Next(hSnapshot, ctypes.byref(pe32)):
                try:
                    name = pe32.szExeFile.decode('utf-8', errors='ignore')
                    processes.append((pe32.th32ProcessID, name))
                except:
                    pass
        
        kernel32.CloseHandle(hSnapshot)
        return processes
        
    except Exception as e:
        print(f"[-] Error listing processes: {e}")
        return []

def main():
    print("[*] Debug Process Test")
    print("=" * 50)
    
    # 首先列出系统中的进程
    print("[1] Listing processes using different methods...")
    
    print("\n[*] Using ctypes (same method as DLL):")
    ctypes_processes = list_processes_with_ctypes()
    notepad_found_ctypes = False
    for pid, name in ctypes_processes:
        if 'notepad' in name.lower():
            print(f"[+] Found notepad: PID={pid}, Name={name}")
            notepad_found_ctypes = True
    
    if not notepad_found_ctypes:
        print("[-] No notepad process found with ctypes method")
    
    print(f"\n[*] Total processes found with ctypes: {len(ctypes_processes)}")
    
    # 使用psutil
    print("\n[*] Using psutil:")
    psutil_processes = list_processes_with_psutil()
    notepad_found_psutil = False
    for pid, name in psutil_processes:
        if 'notepad' in name.lower():
            print(f"[+] Found notepad: PID={pid}, Name={name}")
            notepad_found_psutil = True
    
    if not notepad_found_psutil:
        print("[-] No notepad process found with psutil method")
    
    # 启动notepad如果没有找到
    if not notepad_found_ctypes and not notepad_found_psutil:
        print("\n[2] Starting notepad...")
        try:
            proc = subprocess.Popen(["notepad.exe"])
            print(f"[+] Started notepad.exe (PID: {proc.pid})")
            time.sleep(3)  # 等待启动
            
            # 重新检查
            print("\n[*] Re-checking for notepad...")
            ctypes_processes = list_processes_with_ctypes()
            for pid, name in ctypes_processes:
                if 'notepad' in name.lower():
                    print(f"[+] Now found notepad: PID={pid}, Name={name}")
                    notepad_found_ctypes = True
                    break
        except Exception as e:
            print(f"[-] Failed to start notepad: {e}")
    
    # 测试DLL
    print("\n[3] Testing DLL process detection...")
    try:
        dll = load_dll()
        setup_dll_functions(dll)
        
        if not dll.InitializeDriver():
            error = get_last_error(dll)
            raise Exception(f"Driver initialization failed: {error}")
        print("[+] Driver initialized")
        
        # 测试不同的进程名格式
        test_names = ["notepad.exe", "Notepad.exe", "NOTEPAD.EXE"]
        
        for test_name in test_names:
            print(f"\n[*] Testing with name: '{test_name}'")
            proc_info = ProcessInfo()
            
            if dll.GetProcessInfo(test_name.encode('utf-8'), ctypes.byref(proc_info)):
                print(f"[+] SUCCESS! Found process:")
                print(f"    Name: {proc_info.process_name.decode('utf-8')}")
                print(f"    PID: {proc_info.process_id}")
                print(f"    Base: 0x{proc_info.base_address:016X}")
                break
            else:
                error = get_last_error(dll)
                print(f"[-] Failed with '{test_name}': {error}")
        
    except Exception as e:
        print(f"[-] DLL test failed: {e}")
    
    finally:
        try:
            if 'dll' in locals():
                dll.CleanupDriver()
        except:
            pass
    
    print("\n[+] Debug test completed!")

if __name__ == "__main__":
    main()
