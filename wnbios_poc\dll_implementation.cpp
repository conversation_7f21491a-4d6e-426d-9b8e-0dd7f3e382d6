#define BUILDING_DLL
#include "dll_exports.h"
#include "drv.h"
#include <string>
#include <vector>
#include <memory>

// 全局变量
static std::unique_ptr<eneio_lib> g_driver = nullptr;
static std::string g_last_error = "";

// 设置错误信息
void SetLastError(const std::string& error) {
    g_last_error = error;
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        break;
    case DLL_THREAD_ATTACH:
        break;
    case DLL_THREAD_DETACH:
        break;
    case DLL_PROCESS_DETACH:
        if (g_driver) {
            g_driver.reset();
        }
        break;
    }
    return TRUE;
}

// 初始化驱动
BOOL InitializeDriver() {
    try {
        if (g_driver) {
            g_driver.reset();
        }
        g_driver = std::make_unique<eneio_lib>();
        SetLastError("");
        return TRUE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Failed to initialize driver: ") + e.what());
        return FALSE;
    }
}

// 清理驱动
void CleanupDriver() {
    if (g_driver) {
        g_driver.reset();
    }
    SetLastError("");
}

// 获取进程信息
BOOL GetProcessInfo(const char* process_name, ProcessInfo* info) {
    if (!g_driver || !process_name || !info) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        uint64_t base = g_driver->get_process_base(process_name);
        if (!base) {
            SetLastError("Process not found");
            return FALSE;
        }

        uint32_t pid = g_driver->get_process_id(process_name);
        
        info->process_id = pid;
        strncpy_s(info->process_name, sizeof(info->process_name), process_name, _TRUNCATE);
        info->base_address = base;
        info->cr3 = g_driver->cr3;
        
        return TRUE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error getting process info: ") + e.what());
        return FALSE;
    }
}

// 附加到进程
BOOL AttachToProcess(const char* process_name) {
    if (!g_driver || !process_name) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        uint64_t base = g_driver->get_process_base(process_name);
        if (!base) {
            SetLastError("Failed to attach to process");
            return FALSE;
        }
        return TRUE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error attaching to process: ") + e.what());
        return FALSE;
    }
}

// 获取已附加的进程ID
uint32_t GetAttachedProcessId() {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return 0;
    }
    return g_driver->attached_proc;
}

// 读取字节
BOOL ReadMemoryByte(uint64_t address, uint8_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint8_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading byte: ") + e.what());
        return FALSE;
    }
}

// 读取字
BOOL ReadMemoryWord(uint64_t address, uint16_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint16_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading word: ") + e.what());
        return FALSE;
    }
}

// 读取双字
BOOL ReadMemoryDword(uint64_t address, uint32_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint32_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading dword: ") + e.what());
        return FALSE;
    }
}

// 读取四字
BOOL ReadMemoryQword(uint64_t address, uint64_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint64_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading qword: ") + e.what());
        return FALSE;
    }
}

// 读取浮点数
BOOL ReadMemoryFloat(uint64_t address, float* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(float));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading float: ") + e.what());
        return FALSE;
    }
}

// 读取双精度浮点数
BOOL ReadMemoryDouble(uint64_t address, double* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(double));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading double: ") + e.what());
        return FALSE;
    }
}

// 读取字符串
BOOL ReadMemoryString(uint64_t address, char* buffer, uint32_t buffer_size) {
    if (!g_driver || !buffer || buffer_size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, buffer, buffer_size);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading string: ") + e.what());
        return FALSE;
    }
}

// 读取宽字符串
BOOL ReadMemoryWString(uint64_t address, wchar_t* buffer, uint32_t buffer_size) {
    if (!g_driver || !buffer || buffer_size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, buffer, buffer_size * sizeof(wchar_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading wide string: ") + e.what());
        return FALSE;
    }
}

// 读取字节数组
BOOL ReadMemoryBytes(uint64_t address, uint8_t* buffer, uint32_t size) {
    if (!g_driver || !buffer || size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, buffer, size);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading bytes: ") + e.what());
        return FALSE;
    }
}

// 写入字节
BOOL WriteMemoryByte(uint64_t address, uint8_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint8_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing byte: ") + e.what());
        return FALSE;
    }
}

// 写入字
BOOL WriteMemoryWord(uint64_t address, uint16_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint16_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing word: ") + e.what());
        return FALSE;
    }
}

// 写入双字
BOOL WriteMemoryDword(uint64_t address, uint32_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint32_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing dword: ") + e.what());
        return FALSE;
    }
}

// 写入四字
BOOL WriteMemoryQword(uint64_t address, uint64_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint64_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing qword: ") + e.what());
        return FALSE;
    }
}

// 写入浮点数
BOOL WriteMemoryFloat(uint64_t address, float value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(float));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing float: ") + e.what());
        return FALSE;
    }
}

// 写入双精度浮点数
BOOL WriteMemoryDouble(uint64_t address, double value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(double));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing double: ") + e.what());
        return FALSE;
    }
}

// 写入字符串
BOOL WriteMemoryString(uint64_t address, const char* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        size_t len = strlen(value) + 1; // 包含null终止符
        return g_driver->write_virtual_memory(address, (LPVOID)value, len);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing string: ") + e.what());
        return FALSE;
    }
}

// 写入宽字符串
BOOL WriteMemoryWString(uint64_t address, const wchar_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        size_t len = (wcslen(value) + 1) * sizeof(wchar_t); // 包含null终止符
        return g_driver->write_virtual_memory(address, (LPVOID)value, len);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing wide string: ") + e.what());
        return FALSE;
    }
}

// 写入字节数组
BOOL WriteMemoryBytes(uint64_t address, const uint8_t* buffer, uint32_t size) {
    if (!g_driver || !buffer || size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, (LPVOID)buffer, size);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing bytes: ") + e.what());
        return FALSE;
    }
}

// 通用内存读取
BOOL ReadMemoryGeneric(uint64_t address, MemoryData* data) {
    if (!g_driver || !data) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        switch (data->type) {
        case DATA_TYPE_BYTE:
            return ReadMemoryByte(address, &data->byte_val);
        case DATA_TYPE_WORD:
            return ReadMemoryWord(address, &data->word_val);
        case DATA_TYPE_DWORD:
            return ReadMemoryDword(address, &data->dword_val);
        case DATA_TYPE_QWORD:
            return ReadMemoryQword(address, &data->qword_val);
        case DATA_TYPE_FLOAT:
            return ReadMemoryFloat(address, &data->float_val);
        case DATA_TYPE_DOUBLE:
            return ReadMemoryDouble(address, &data->double_val);
        default:
            SetLastError("Unsupported data type for generic read");
            return FALSE;
        }
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error in generic read: ") + e.what());
        return FALSE;
    }
}

// 通用内存写入
BOOL WriteMemoryGeneric(uint64_t address, const MemoryData* data) {
    if (!g_driver || !data) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        switch (data->type) {
        case DATA_TYPE_BYTE:
            return WriteMemoryByte(address, data->byte_val);
        case DATA_TYPE_WORD:
            return WriteMemoryWord(address, data->word_val);
        case DATA_TYPE_DWORD:
            return WriteMemoryDword(address, data->dword_val);
        case DATA_TYPE_QWORD:
            return WriteMemoryQword(address, data->qword_val);
        case DATA_TYPE_FLOAT:
            return WriteMemoryFloat(address, data->float_val);
        case DATA_TYPE_DOUBLE:
            return WriteMemoryDouble(address, data->double_val);
        case DATA_TYPE_STRING:
            return WriteMemoryString(address, data->string_val);
        case DATA_TYPE_WSTRING:
            return WriteMemoryWString(address, data->wstring_val);
        case DATA_TYPE_BYTES:
            return WriteMemoryBytes(address, data->bytes_val.data, data->bytes_val.size);
        default:
            SetLastError("Unsupported data type for generic write");
            return FALSE;
        }
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error in generic write: ") + e.what());
        return FALSE;
    }
}

// 列出运行的进程
BOOL ListRunningProcesses(ProcessInfo* processes, uint32_t* count, uint32_t max_count) {
    if (!g_driver || !processes || !count) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        // 这里需要修改原始的list_running_processes函数来返回数据而不是打印
        // 暂时返回FALSE，需要实现一个新的函数
        SetLastError("Function not yet implemented - requires modification of original code");
        return FALSE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error listing processes: ") + e.what());
        return FALSE;
    }
}

// 按关键词搜索进程
BOOL SearchProcessesByKeyword(const char* keyword, ProcessInfo* processes, uint32_t* count, uint32_t max_count) {
    if (!g_driver || !keyword || !processes || !count) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        // 这里需要修改原始的search_processes_by_keyword函数来返回数据而不是打印
        // 暂时返回FALSE，需要实现一个新的函数
        SetLastError("Function not yet implemented - requires modification of original code");
        return FALSE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error searching processes: ") + e.what());
        return FALSE;
    }
}

// 枚举进程模块
BOOL EnumerateProcessModules(const char* process_name, ModuleInfo* modules, uint32_t* count, uint32_t max_count) {
    if (!g_driver || !process_name || !modules || !count) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        // 这里需要修改原始的enumerate_process_modules函数来返回数据而不是打印
        // 暂时返回FALSE，需要实现一个新的函数
        SetLastError("Function not yet implemented - requires modification of original code");
        return FALSE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error enumerating modules: ") + e.what());
        return FALSE;
    }
}

// 查找模块基址
uint64_t FindModuleBase(const char* process_name, const wchar_t* module_name) {
    if (!g_driver || !process_name || !module_name) {
        SetLastError("Invalid parameters or driver not initialized");
        return 0;
    }

    try {
        return g_driver->find_module_base(process_name, module_name);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error finding module base: ") + e.what());
        return 0;
    }
}

// 虚拟地址转物理地址
uint64_t VirtualToPhysical(uint64_t virtual_address) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return 0;
    }

    try {
        return g_driver->convert_virtual_to_physical(virtual_address);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error converting address: ") + e.what());
        return 0;
    }
}

// 检查地址是否有效
BOOL IsValidAddress(uint64_t address) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        // 尝试读取一个字节来检查地址是否有效
        uint8_t test_byte;
        return g_driver->read_virtual_memory(address, &test_byte, sizeof(uint8_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error checking address validity: ") + e.what());
        return FALSE;
    }
}

// 获取最后的驱动错误
BOOL GetLastDriverError(char* error_buffer, uint32_t buffer_size) {
    if (!error_buffer || buffer_size == 0) {
        return FALSE;
    }

    strncpy_s(error_buffer, buffer_size, g_last_error.c_str(), _TRUNCATE);
    return TRUE;
}
