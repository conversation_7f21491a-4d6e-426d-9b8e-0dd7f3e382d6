#include "drv.h"

bool eneio_lib::to_file()
{
	if (std::filesystem::exists(store_at + drv_name))
		return 1;

	std::filesystem::create_directories(store_at);

	std::ofstream out_driver(store_at + drv_name, std::ios::beg | std::ios::binary);
	if (!out_driver.is_open())
		return 0;

	for (auto& c : driver::eneio64)
		out_driver << c;
	out_driver.close();

	return 1;
}

bool eneio_lib::create_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = CreateService(sc_manager, service_name.c_str(), NULL,
		SERVICE_ALL_ACCESS,
		SERVICE_KERNEL_DRIVER,
		SERVICE_DEMAND_START,
		SERVICE_ERROR_NORMAL,
		(store_at + drv_name).c_str(),
		NULL,
		NULL,
		NULL,
		NULL,
		NULL);

	if (service == NULL) {

		service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

		if (service == NULL) {
			CloseServiceHandle(sc_manager);
			return 0;
		}
	}

	CloseServiceHandle(sc_manager);

	return 1;
}

bool eneio_lib::start_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 0;
	}

	if (StartService(service, 0, NULL) == NULL) {
		CloseServiceHandle(sc_manager);
		CloseServiceHandle(service);
		return 0;
	}

	CloseServiceHandle(sc_manager);
	return 1;
}

bool eneio_lib::stop_service()
{
	SERVICE_STATUS ss;
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 0;
	}

	if (ControlService(service, SERVICE_CONTROL_STOP, &ss) == NULL) {
		CloseServiceHandle(sc_manager);
		CloseServiceHandle(service);
		return 0;

	}

	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1;
}

bool eneio_lib::delete_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 0;
	}

	DeleteService(service);
	CloseServiceHandle(sc_manager);

	return 1;
}

bool eneio_lib::stop_service_silent()
{
	SERVICE_STATUS ss;
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 0;
	}

	ControlService(service, SERVICE_CONTROL_STOP, &ss);
	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1;
}

bool eneio_lib::delete_service_silent()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 0;
	}

	DeleteService(service);
	CloseServiceHandle(sc_manager);
	return 1;
}

void eneio_lib::force_cleanup_service()
{
	// 使用系统命令强制清理服务
	std::string stop_cmd = "sc stop " + service_name + " >nul 2>&1";
	std::string delete_cmd = "sc delete " + service_name + " >nul 2>&1";
	
	system(stop_cmd.c_str());
	Sleep(500); // 等待服务停止
	system(delete_cmd.c_str());
	Sleep(500); // 等待服务删除
}

void eneio_lib::get_eprocess_offsets() {

	NTSTATUS(WINAPI * RtlGetVersion)(LPOSVERSIONINFOEXW);
	OSVERSIONINFOEXW osInfo;

	*(FARPROC*)&RtlGetVersion = GetProcAddress(GetModuleHandleA("ntdll"),
		"RtlGetVersion");

	DWORD build = 0;

	if (NULL != RtlGetVersion)
	{
		osInfo.dwOSVersionInfoSize = sizeof(osInfo);
		RtlGetVersion(&osInfo);
		build = osInfo.dwBuildNumber;
	}

	switch (build) 
	{
	case 22000: //WIN11
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19045: // WIN10_22H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19044: //WIN10_21H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19043: //WIN10_21H1
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19042: //WIN10_20H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19041: //WIN10_20H1
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 18363: //WIN10_19H2
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x340;
		EP_SECTIONBASE = 0x3c8;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 18362: //WIN10_19H1
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x340;
		EP_SECTIONBASE = 0x3c8;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 17763: //WIN10_RS5
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 17134: //WIN10_RS4
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 16299: //WIN10_RS3
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 15063: //WIN10_RS2
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 14393: //WIN10_RS1
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	default:
		exit(0);
		break;
	}
}

uintptr_t eneio_lib::leak_kprocess()
{
	std::vector<uintptr_t> pointers;

	if (!leak_kpointers(pointers))
	{
		return false;
	}

	const unsigned int sanity_check = 0x3;

	for (uintptr_t pointer : pointers)
	{
		unsigned int check = 0;

		read_virtual_memory(pointer, &check, sizeof(unsigned int));

		if (check == sanity_check)
		{
			return pointer;
			break;
		}
	}

	return NULL;
}


bool eneio_lib::leak_kpointers(std::vector<uintptr_t>& pointers)
{
	const unsigned long SystemExtendedHandleInformation = 0x40;

	unsigned long buffer_length = 0;
	unsigned char tempbuffer[1024] = { 0 };
	NTSTATUS status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), &tempbuffer, sizeof(tempbuffer), &buffer_length);

	buffer_length += 50 * (sizeof(SYSTEM_HANDLE_INFORMATION_EX) + sizeof(SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX));

	PVOID buffer = VirtualAlloc(nullptr, buffer_length, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);

	RtlSecureZeroMemory(buffer, buffer_length);

	unsigned long buffer_length_correct = 0;
	status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), buffer, buffer_length, &buffer_length_correct);

	SYSTEM_HANDLE_INFORMATION_EX* handle_information = reinterpret_cast<SYSTEM_HANDLE_INFORMATION_EX*>(buffer);

	for (unsigned int i = 0; i < handle_information->NumberOfHandles; i++)
	{
		const unsigned int SystemUniqueReserved = 4;
		const unsigned int SystemKProcessHandleAttributes = 0x102A;

		if (handle_information->Handles[i].UniqueProcessId == SystemUniqueReserved &&
			handle_information->Handles[i].HandleAttributes == SystemKProcessHandleAttributes)
		{
			pointers.push_back(reinterpret_cast<uintptr_t>(handle_information->Handles[i].Object));
		}
	}

	VirtualFree(buffer, 0, MEM_RELEASE);
	return true;
}


uintptr_t eneio_lib::map_physical(uint64_t address, size_t size, eneio_mem& mem)
{
	memset(&mem, 0, sizeof(eneio_mem));
	mem.addr = address;
	mem.size = size;
	DWORD retSize;
	auto status = DeviceIoControl(hHandle, 0x80102040, &mem, sizeof(eneio_mem), &mem, sizeof(eneio_mem), &retSize, 0);
	if (!status)
		return 0;
	
	return mem.outPtr;
}

uintptr_t eneio_lib::unmap_physical(eneio_mem& mem)
{
	DWORD bytes_returned;
	auto status = DeviceIoControl(hHandle, 0x80102044, &mem, sizeof(eneio_mem), 0, 0, &bytes_returned, 0);
	if (!status)
		return 0;

	return 1;
}

uintptr_t eneio_lib::get_system_dirbase()
{
	for (int i = 0; i < 10; i++)
	{
		eneio_mem mem;
		uintptr_t lpBuffer = map_physical(i * 0x10000, 0x10000, mem);

		for (int uOffset = 0; uOffset < 0x10000; uOffset += 0x1000)
		{
			if (0x00000001000600E9 ^ (0xffffffffffff00ff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset)))
				continue;
			if (0xfffff80000000000 ^ (0xfffff80000000000 & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0x70)))
				continue;
			if (0xffffff0000000fff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0))
				continue;

			return *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0);
		}

		unmap_physical(mem);
	}

	return NULL;
}

uintptr_t eneio_lib::get_process_id(const char* image_name)
{
	HANDLE hsnap;
	PROCESSENTRY32 pt;
	DWORD PiD = 0;

	hsnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hsnap == INVALID_HANDLE_VALUE) {
		return 0;
	}

	pt.dwSize = sizeof(PROCESSENTRY32);

	// 必须先调用 Process32First
	if (Process32First(hsnap, &pt)) {
		do {
			if (!strcmp(pt.szExeFile, image_name)) {
				PiD = pt.th32ProcessID;
				CloseHandle(hsnap);
				return PiD;
			}
		} while (Process32Next(hsnap, &pt));
	}

	CloseHandle(hsnap);
	return 0; // 未找到进程返回0
}

uintptr_t eneio_lib::get_process_base(const char* image_name)
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();

	if (!kprocess_initial)
		return NULL;

	printf("system_kprocess: %llx\n", kprocess_initial);
	printf("system_cr3: %llx\n", cr3);

	const unsigned long limit = 800; // 增加遍历限制以找到更多进程

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	uintptr_t image_base_out = 0;


	// 使用更安全的遍历方法
	std::vector<uintptr_t> seen_kprocess;
	std::vector<int> seen_pids;
	
	uintptr_t current_kprocess = kprocess_initial;
	const int max_processes = 200;
	
	for (int i = 0; i < max_processes; i++)
	{
		// 检查是否已访问过
		bool already_visited = false;
		for (uintptr_t addr : seen_kprocess) {
			if (addr == current_kprocess) {
				already_visited = true;
				break;
			}
		}
		
		if (already_visited) {
			printf("[DEBUG] Loop detected, stopping\n");
			break;
		}
		
		seen_kprocess.push_back(current_kprocess);

		uintptr_t virtual_size = read_virtual_memory<uintptr_t>(current_kprocess + EP_VIRTUALSIZE);
		if (virtual_size == 0) {
			// 获取下一个进程
			uintptr_t flink = 0;
			read_virtual_memory(current_kprocess + EP_ACTIVEPROCESSLINK, &flink, sizeof(flink));
			if (flink == 0) break;
			current_kprocess = flink - EP_ACTIVEPROCESSLINK;
			continue;
		}

		uintptr_t directory_table = read_virtual_memory<uintptr_t>(current_kprocess + EP_DIRECTORYTABLE);
		uintptr_t base_address = read_virtual_memory<uintptr_t>(current_kprocess + EP_SECTIONBASE);

		char name[16] = { };
		read_virtual_memory(current_kprocess + EP_IMAGEFILENAME, &name, sizeof(name));

		int process_id = 0;
		read_virtual_memory(current_kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		// 验证进程ID有效性
		if (process_id <= 0 || process_id >= 100000) {
			// 获取下一个进程
			uintptr_t flink = 0;
			read_virtual_memory(current_kprocess + EP_ACTIVEPROCESSLINK, &flink, sizeof(flink));
			if (flink == 0) break;
			current_kprocess = flink - EP_ACTIVEPROCESSLINK;
			continue;
		}

		// 检查重复PID
		bool already_seen = false;
		for (int pid : seen_pids) {
			if (pid == process_id) {
				already_seen = true;
				break;
			}
		}
		
		if (already_seen) {
			printf("[DEBUG] Duplicate PID %d, stopping\n", process_id);
			break;
		}
		
		seen_pids.push_back(process_id);

		// 改进的进程名称匹配逻辑
		bool name_match = false;
		
		if (strlen(name) > 0 && strncmp(name, image_name, strlen(name)) == 0) {
			name_match = true;
		}
		else if (strlen(name) > 0 && strstr(image_name, name) != NULL) {
			name_match = true;
		}
		else if (strncmp(name, "DeltaForceClie", 14) == 0 && strstr(image_name, "DeltaForce") != NULL) {
			name_match = true;
		}
		
		// 只显示相关进程
		if (strstr(image_name, "Delta") != NULL || strstr(name, "Delta") != NULL || name_match) {
			printf("[DEBUG] Checking process: kernel='%s', input='%s', PID=%d\n", name, image_name, process_id);
		}
		
		if (name_match && process_id == get_process_id(image_name))
		{
			printf("[FOUND] Matching process:\n");
			printf("  Kernel name: %s\n", name);
			printf("  Full name: %s\n", image_name);
			printf("  PID: %d\n", process_id);
			printf("  Base: 0x%llx\n", base_address);
			printf("  CR3: 0x%llx\n", directory_table);

			image_base_out = base_address;
			cr3 = directory_table;
			attached_proc = process_id;

			break;
		}
		
		// 获取下一个进程
		uintptr_t flink = 0;
		read_virtual_memory(current_kprocess + EP_ACTIVEPROCESSLINK, &flink, sizeof(flink));
		if (flink == 0) break;
		current_kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		// 检查是否回到起始点
		if (current_kprocess == kprocess_initial && i > 0) {
			printf("[DEBUG] Completed full circle\n");
			break;
		}
	}
	
	return image_base_out;
}

bool eneio_lib::read_physical_memory(uintptr_t physical_address, void* output, unsigned long size)
{
	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(output, reinterpret_cast<void*>(virtual_address), size);
	unmap_physical(mem);
	return true;
}

bool eneio_lib::write_physical_memory(uintptr_t physical_address, void* data, unsigned long size)
{
	if (!data)
		return false;

	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(reinterpret_cast<void*>(virtual_address), reinterpret_cast<void*>(data), size);
	unmap_physical(mem);
	return true;
}

uintptr_t eneio_lib::convert_virtual_to_physical(uintptr_t virtual_address)
{
	uintptr_t va = virtual_address;

	unsigned short PML4 = (unsigned short)((va >> 39) & 0x1FF);
	uintptr_t PML4E = 0;
	read_physical_memory((cr3 + PML4 * sizeof(uintptr_t)), &PML4E, sizeof(PML4E));

	if (PML4E == 0)
		return 0;

	unsigned short DirectoryPtr = (unsigned short)((va >> 30) & 0x1FF);
	uintptr_t PDPTE = 0;
	read_physical_memory(((PML4E & 0xFFFFFFFFFF000) + DirectoryPtr * sizeof(uintptr_t)), &PDPTE, sizeof(PDPTE));

	if (PDPTE == 0)
		return 0;

	if ((PDPTE & (1 << 7)) != 0)
		return (PDPTE & 0xFFFFFC0000000) + (va & 0x3FFFFFFF);

	unsigned short Directory = (unsigned short)((va >> 21) & 0x1FF);

	uintptr_t PDE = 0;
	read_physical_memory(((PDPTE & 0xFFFFFFFFFF000) + Directory * sizeof(uintptr_t)), &PDE, sizeof(PDE));

	if (PDE == 0)
		return 0;

	if ((PDE & (1 << 7)) != 0)
	{
		return (PDE & 0xFFFFFFFE00000) + (va & 0x1FFFFF);
	}

	unsigned short Table = (unsigned short)((va >> 12) & 0x1FF);
	uintptr_t PTE = 0;

	read_physical_memory(((PDE & 0xFFFFFFFFFF000) + Table * sizeof(uintptr_t)), &PTE, sizeof(PTE));

	if (PTE == 0)
		return 0;

	return (PTE & 0xFFFFFFFFFF000) + (va & 0xFFF);
}

bool eneio_lib::read_virtual_memory(uintptr_t address, LPVOID output, unsigned long size)
{
	if (!address)
		return false;

	if (!size)
		return false;

	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	bool result = read_physical_memory(physical_address, output, size);
	
	return result;
}

bool eneio_lib::write_virtual_memory(uintptr_t address, LPVOID data, unsigned long size)
{
	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	write_physical_memory(physical_address, data, size);
	return true;
}

uintptr_t eneio_lib::get_process_peb(const char* process_name)
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial)
		return NULL;

	const unsigned long limit = 400;
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		char name[16] = { };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		if (strstr(process_name, name) && process_id == get_process_id(process_name))
		{
			// 获取 PEB 地址 (EPROCESS + 0x550 在大多数版本中)
			uintptr_t peb_address = 0;
			read_virtual_memory(kprocess + 0x550, &peb_address, sizeof(peb_address));
			
			uintptr_t directory_table = read_virtual_memory<uintptr_t>(kprocess + EP_DIRECTORYTABLE);
			cr3 = directory_table;
			attached_proc = process_id;
			
			return peb_address;
		}
	}
	
	return NULL;
}

void eneio_lib::enumerate_process_modules(const char* process_name)
{
	printf("[*] Enumerating modules for process: %s\n", process_name);
	
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		printf("[-] Failed to get PEB for process %s\n", process_name);
		return;
	}
	
	printf("[+] PEB address: 0x%llx\n", peb);
	
	// PEB + 0x18 = PEB_LDR_DATA
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		printf("[-] Failed to read LDR data pointer\n");
		return;
	}
	
	printf("[+] LDR data address: 0x%llx\n", ldr_data_ptr);
	
	// PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		printf("[-] Failed to read module list\n");
		return;
	}
	
	printf("[+] Module list head: 0x%llx\n", module_list);
	
	uintptr_t current_entry = module_list;
	int module_count = 0;
	
	printf("[*] Starting module enumeration...\n");
	
	do {
		if (module_count > 100) break; // 防止无限循环
		
		// 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
		// InMemoryOrderLinks 在 LDR_DATA_TABLE_ENTRY + 0x10
		uintptr_t ldr_entry = current_entry - 0x10;
		
		// LDR_DATA_TABLE_ENTRY 结构
		uintptr_t dll_base = 0;
		uintptr_t size_of_image = 0;
		
		// DllBase 在偏移 0x30
		if (!read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
			break;
		}
		
		// SizeOfImage 在偏移 0x40  
		if (!read_virtual_memory(ldr_entry + 0x40, &size_of_image, sizeof(size_of_image))) {
			break;
		}
		
		// BaseDllName 在偏移 0x58 (UNICODE_STRING)
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, module_name, min(unicode_string.Length, sizeof(module_name) - 2))) {
					// 只显示前10个模块的详细信息，避免输出过多
					if (module_count < 10) {
						printf("[+] Module %d:\n", module_count);
						printf("    Name: %ws\n", module_name);
						printf("    Base: 0x%llx\n", dll_base);
						printf("    Size: 0x%x\n", (unsigned int)size_of_image);
						printf("\n");
					} else if (module_count == 10) {
						printf("[*] ... (showing only first 10 modules)\n");
					}
				}
			}
		}
		
		// 获取下一个条目
		if (!read_virtual_memory(current_entry, &current_entry, sizeof(current_entry))) {
			break;
		}
		
		module_count++;
		
	} while (current_entry != module_list && current_entry != 0);
	
	printf("[+] Total modules found: %d\n", module_count);
}

void eneio_lib::simple_module_test(const char* process_name)
{
	printf("[*] Simple module test for: %s\n", process_name);
	
	// 首先获取进程基址
	uintptr_t base = get_process_base(process_name);
	if (!base) {
		printf("[-] Failed to get process base\n");
		return;
	}
	
	printf("[+] Process base: 0x%llx\n", base);
	
	// 尝试读取PE头
	UINT8 pe_header[64] = { 0 };
	if (read_virtual_memory(base, pe_header, 64)) {
		printf("[+] Successfully read PE header:\n");
		printf("    ");
		for (int i = 0; i < 16; i++) {
			printf("%02X ", pe_header[i]);
		}
		printf("\n");
		
		if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) {
			printf("[+] Valid PE signature found!\n");
		}
	} else {
		printf("[-] Failed to read PE header\n");
	}
}

void eneio_lib::list_running_processes()
{
	printf("[*] Listing running processes...\n");
	
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[-] Failed to get system directory base\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[-] Failed to get initial kprocess\n");
		return;
	}

	printf("\n%-6s %-20s %-16s\n", "PID", "Process Name", "Base Address");
	printf("%-6s %-20s %-16s\n", "------", "--------------------", "----------------");

	// 简化的遍历方法 - 回到原始逻辑但加入重复检测
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	int process_count = 0;
	std::vector<int> seen_pids; // 只检测PID重复
	
	// 读取第一个flink
	read_virtual_memory(flink, &flink, sizeof(PVOID));
	
	do {
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		// 读取进程信息
		char name[16] = { 0 };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));
		
		uintptr_t base_address = 0;
		read_virtual_memory(kprocess + EP_SECTIONBASE, &base_address, sizeof(base_address));
		
		// 验证进程信息并检查重复
		if (process_id > 0 && process_id < 100000 && strlen(name) > 0) {
			bool already_seen = false;
			for (int pid : seen_pids) {
				if (pid == process_id) {
					already_seen = true;
					break;
				}
			}
			
			if (!already_seen) {
				printf("%-6d %-20s 0x%llx\n", process_id, name, base_address);
				seen_pids.push_back(process_id);
				process_count++;
			}
		}
		
		// 获取下一个链表项
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		
		// 安全检查：如果进程数量过多，可能有问题
		if (process_count > 500) {
			printf("[*] Too many processes, stopping enumeration\n");
			break;
		}
		
	} while (flink != link_start && flink != 0);
	
	printf("\n[+] Total unique processes found: %d\n", process_count);
}

void eneio_lib::search_processes_by_keyword(const char* keyword)
{
	printf("[*] Searching for processes containing keyword: %s\n", keyword);
	
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[-] Failed to get system directory base\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[-] Failed to get initial kprocess\n");
		return;
	}

	const unsigned long limit = 800;
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	int found_count = 0;

	printf("\n%-6s %-30s %-16s\n", "PID", "Process Name", "Base Address");
	printf("%-6s %-30s %-16s\n", "------", "------------------------------", "----------------");

	std::vector<int> seen_pids; // 避免重复
	
	for (int a = 0; a < limit; a++)
	{
		if (a > 0 && flink == link_start) break;
		
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		if (flink == 0) break;
		
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		char name[16] = { 0 };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));
		
		// 检查重复
		bool already_seen = false;
		for (int pid : seen_pids) {
			if (pid == process_id) {
				already_seen = true;
				break;
			}
		}
		if (already_seen) break;
		
		uintptr_t base_address = 0;
		read_virtual_memory(kprocess + EP_SECTIONBASE, &base_address, sizeof(base_address));

		// 检查进程名是否包含关键词（不区分大小写）
		if (process_id > 0 && strlen(name) > 0) {
			seen_pids.push_back(process_id);
			
			char lower_name[16] = { 0 };
			char lower_keyword[256] = { 0 };
			
			// 转换为小写进行比较
			for (int i = 0; i < strlen(name); i++) {
				lower_name[i] = tolower(name[i]);
			}
			for (int i = 0; i < strlen(keyword); i++) {
				lower_keyword[i] = tolower(keyword[i]);
			}
			
			if (strstr(lower_name, lower_keyword) != NULL) {
				printf("%-6d %-30s 0x%llx\n", process_id, name, base_address);
				found_count++;
			}
		}
	}
	
	printf("\n[+] Found %d processes containing '%s'\n", found_count, keyword);
}

uintptr_t eneio_lib::find_module_base(const char* process_name, const wchar_t* module_name)
{
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		return 0;
	}
	
	// PEB + 0x18 = PEB_LDR_DATA
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		return 0;
	}
	
	// PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		return 0;
	}
	
	uintptr_t current_entry = module_list;
	int module_count = 0;
	
	do {
		if (module_count > 100) break;
		
		// 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
		uintptr_t ldr_entry = current_entry - 0x10;
		
		// DllBase 在偏移 0x30
		uintptr_t dll_base = 0;
		if (!read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
			break;
		}
		
		// BaseDllName 在偏移 0x58 (UNICODE_STRING)
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t current_module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, current_module_name, min(unicode_string.Length, sizeof(current_module_name) - 2))) {
					// 比较模块名称（不区分大小写）
					if (wcscmp(current_module_name, module_name) == 0 || 
						_wcsicmp(current_module_name, module_name) == 0) {
						return dll_base;
					}
				}
			}
		}
		
		// 获取下一个条目
		if (!read_virtual_memory(current_entry, &current_entry, sizeof(current_entry))) {
			break;
		}
		
		module_count++;
		
	} while (current_entry != module_list && current_entry != 0);
	
	return 0; // 未找到
}

void eneio_lib::test_simple_module_read(const char* process_name)
{
	printf("[*] Testing simple module read for: %s\n", process_name);
	
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		printf("[-] Failed to get PEB\n");
		return;
	}
	
	printf("[+] PEB: 0x%llx\n", peb);
	
	// 读取 LDR 指针
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		printf("[-] Failed to read LDR pointer\n");
		return;
	}
	
	printf("[+] LDR: 0x%llx\n", ldr_data_ptr);
	
	// 读取模块列表头
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		printf("[-] Failed to read module list\n");
		return;
	}
	
	printf("[+] Module list: 0x%llx\n", module_list);
	
	// 尝试读取第一个模块的基址
	// 需要从链表条目减去0x10偏移得到LDR_DATA_TABLE_ENTRY
	uintptr_t ldr_entry = module_list - 0x10;
	uintptr_t dll_base = 0;
	if (read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
		printf("[+] First module base (corrected): 0x%llx\n", dll_base);
		
		// 尝试读取模块名称
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, module_name, min(unicode_string.Length, sizeof(module_name) - 2))) {
					printf("[+] First module name: %ws\n", module_name);
				}
			}
		}
	} else {
		printf("[-] Failed to read first module base\n");
	}
}