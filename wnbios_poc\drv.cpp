#define BUILDING_DLL
#include "dll_exports.h"
#include "drv.h"
#include <string>
#include <vector>
#include <memory>

// 全局变量
static std::unique_ptr<eneio_lib> g_driver = nullptr;
static std::string g_last_error = "";

// 设置错误信息
void SetLastError(const std::string& error) {
    g_last_error = error;
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        break;
    case DLL_THREAD_ATTACH:
        break;
    case DLL_THREAD_DETACH:
        break;
    case DLL_PROCESS_DETACH:
        if (g_driver) {
            g_driver.reset();
        }
        break;
    }
    return TRUE;
}

// 初始化驱动
BOOL InitializeDriver() {
    try {
        if (g_driver) {
            g_driver.reset();
        }
        g_driver = std::make_unique<eneio_lib>();
        SetLastError("");
        return TRUE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Failed to initialize driver: ") + e.what());
        return FALSE;
    }
}

// 清理驱动
void CleanupDriver() {
    if (g_driver) {
        g_driver.reset();
    }
    SetLastError("");
}

// 获取进程信息
BOOL GetProcessInfo(const char* process_name, ProcessInfo* info) {
    if (!g_driver || !process_name || !info) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        uint64_t base = g_driver->get_process_base(process_name);
        if (!base) {
            SetLastError("Process not found");
            return FALSE;
        }

        uint32_t pid = g_driver->get_process_id(process_name);
        
        info->process_id = pid;
        strncpy_s(info->process_name, sizeof(info->process_name), process_name, _TRUNCATE);
        info->base_address = base;
        info->cr3 = g_driver->cr3;
        
        return TRUE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error getting process info: ") + e.what());
        return FALSE;
    }
}

// 附加到进程
BOOL AttachToProcess(const char* process_name) {
    if (!g_driver || !process_name) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        uint64_t base = g_driver->get_process_base(process_name);
        if (!base) {
            SetLastError("Failed to attach to process");
            return FALSE;
        }
        return TRUE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error attaching to process: ") + e.what());
        return FALSE;
    }
}

// 获取已附加的进程ID
uint32_t GetAttachedProcessId() {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return 0;
    }
    return g_driver->attached_proc;
}

// 读取字节
BOOL ReadMemoryByte(uint64_t address, uint8_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint8_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading byte: ") + e.what());
        return FALSE;
    }
}

// 读取字
BOOL ReadMemoryWord(uint64_t address, uint16_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint16_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading word: ") + e.what());
        return FALSE;
    }
}

// 读取双字
BOOL ReadMemoryDword(uint64_t address, uint32_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint32_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading dword: ") + e.what());
        return FALSE;
    }
}

// 读取四字
BOOL ReadMemoryQword(uint64_t address, uint64_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(uint64_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading qword: ") + e.what());
        return FALSE;
    }
}

// 读取浮点数
BOOL ReadMemoryFloat(uint64_t address, float* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(float));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading float: ") + e.what());
        return FALSE;
    }
}

// 读取双精度浮点数
BOOL ReadMemoryDouble(uint64_t address, double* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, value, sizeof(double));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading double: ") + e.what());
        return FALSE;
    }
}

// 读取字符串
BOOL ReadMemoryString(uint64_t address, char* buffer, uint32_t buffer_size) {
    if (!g_driver || !buffer || buffer_size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, buffer, buffer_size);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading string: ") + e.what());
        return FALSE;
    }
}

// 读取宽字符串
BOOL ReadMemoryWString(uint64_t address, wchar_t* buffer, uint32_t buffer_size) {
    if (!g_driver || !buffer || buffer_size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, buffer, buffer_size * sizeof(wchar_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading wide string: ") + e.what());
        return FALSE;
    }
}

// 读取字节数组
BOOL ReadMemoryBytes(uint64_t address, uint8_t* buffer, uint32_t size) {
    if (!g_driver || !buffer || size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->read_virtual_memory(address, buffer, size);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error reading bytes: ") + e.what());
        return FALSE;
    }
}

// 写入字节
BOOL WriteMemoryByte(uint64_t address, uint8_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint8_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing byte: ") + e.what());
        return FALSE;
    }
}

// 写入字
BOOL WriteMemoryWord(uint64_t address, uint16_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint16_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing word: ") + e.what());
        return FALSE;
    }
}

// 写入双字
BOOL WriteMemoryDword(uint64_t address, uint32_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint32_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing dword: ") + e.what());
        return FALSE;
    }
}

// 写入四字
BOOL WriteMemoryQword(uint64_t address, uint64_t value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(uint64_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing qword: ") + e.what());
        return FALSE;
    }
}

// 写入浮点数
BOOL WriteMemoryFloat(uint64_t address, float value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(float));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing float: ") + e.what());
        return FALSE;
    }
}

// 写入双精度浮点数
BOOL WriteMemoryDouble(uint64_t address, double value) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, &value, sizeof(double));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing double: ") + e.what());
        return FALSE;
    }
}

// 写入字符串
BOOL WriteMemoryString(uint64_t address, const char* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        size_t len = strlen(value) + 1; // 包含null终止符
        return g_driver->write_virtual_memory(address, (LPVOID)value, len);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing string: ") + e.what());
        return FALSE;
    }
}

// 写入宽字符串
BOOL WriteMemoryWString(uint64_t address, const wchar_t* value) {
    if (!g_driver || !value) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        size_t len = (wcslen(value) + 1) * sizeof(wchar_t); // 包含null终止符
        return g_driver->write_virtual_memory(address, (LPVOID)value, len);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing wide string: ") + e.what());
        return FALSE;
    }
}

// 写入字节数组
BOOL WriteMemoryBytes(uint64_t address, const uint8_t* buffer, uint32_t size) {
    if (!g_driver || !buffer || size == 0) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        return g_driver->write_virtual_memory(address, (LPVOID)buffer, size);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error writing bytes: ") + e.what());
        return FALSE;
    }
}

// 通用内存读取
BOOL ReadMemoryGeneric(uint64_t address, MemoryData* data) {
    if (!g_driver || !data) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        switch (data->type) {
        case DATA_TYPE_BYTE:
            return ReadMemoryByte(address, &data->byte_val);
        case DATA_TYPE_WORD:
            return ReadMemoryWord(address, &data->word_val);
        case DATA_TYPE_DWORD:
            return ReadMemoryDword(address, &data->dword_val);
        case DATA_TYPE_QWORD:
            return ReadMemoryQword(address, &data->qword_val);
        case DATA_TYPE_FLOAT:
            return ReadMemoryFloat(address, &data->float_val);
        case DATA_TYPE_DOUBLE:
            return ReadMemoryDouble(address, &data->double_val);
        default:
            SetLastError("Unsupported data type for generic read");
            return FALSE;
        }
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error in generic read: ") + e.what());
        return FALSE;
    }
}

// 通用内存写入
BOOL WriteMemoryGeneric(uint64_t address, const MemoryData* data) {
    if (!g_driver || !data) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        switch (data->type) {
        case DATA_TYPE_BYTE:
            return WriteMemoryByte(address, data->byte_val);
        case DATA_TYPE_WORD:
            return WriteMemoryWord(address, data->word_val);
        case DATA_TYPE_DWORD:
            return WriteMemoryDword(address, data->dword_val);
        case DATA_TYPE_QWORD:
            return WriteMemoryQword(address, data->qword_val);
        case DATA_TYPE_FLOAT:
            return WriteMemoryFloat(address, data->float_val);
        case DATA_TYPE_DOUBLE:
            return WriteMemoryDouble(address, data->double_val);
        case DATA_TYPE_STRING:
            return WriteMemoryString(address, data->string_val);
        case DATA_TYPE_WSTRING:
            return WriteMemoryWString(address, data->wstring_val);
        case DATA_TYPE_BYTES:
            return WriteMemoryBytes(address, data->bytes_val.data, data->bytes_val.size);
        default:
            SetLastError("Unsupported data type for generic write");
            return FALSE;
        }
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error in generic write: ") + e.what());
        return FALSE;
    }
}

// 列出运行的进程
BOOL ListRunningProcesses(ProcessInfo* processes, uint32_t* count, uint32_t max_count) {
    if (!g_driver || !processes || !count) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        // 这里需要修改原始的list_running_processes函数来返回数据而不是打印
        // 暂时返回FALSE，需要实现一个新的函数
        SetLastError("Function not yet implemented - requires modification of original code");
        return FALSE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error listing processes: ") + e.what());
        return FALSE;
    }
}

// 按关键词搜索进程
BOOL SearchProcessesByKeyword(const char* keyword, ProcessInfo* processes, uint32_t* count, uint32_t max_count) {
    if (!g_driver || !keyword || !processes || !count) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        // 这里需要修改原始的search_processes_by_keyword函数来返回数据而不是打印
        // 暂时返回FALSE，需要实现一个新的函数
        SetLastError("Function not yet implemented - requires modification of original code");
        return FALSE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error searching processes: ") + e.what());
        return FALSE;
    }
}

// 枚举进程模块
BOOL EnumerateProcessModules(const char* process_name, ModuleInfo* modules, uint32_t* count, uint32_t max_count) {
    if (!g_driver || !process_name || !modules || !count) {
        SetLastError("Invalid parameters or driver not initialized");
        return FALSE;
    }

    try {
        // 这里需要修改原始的enumerate_process_modules函数来返回数据而不是打印
        // 暂时返回FALSE，需要实现一个新的函数
        SetLastError("Function not yet implemented - requires modification of original code");
        return FALSE;
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error enumerating modules: ") + e.what());
        return FALSE;
    }
}

// 查找模块基址
uint64_t FindModuleBase(const char* process_name, const wchar_t* module_name) {
    if (!g_driver || !process_name || !module_name) {
        SetLastError("Invalid parameters or driver not initialized");
        return 0;
    }

    try {
        return g_driver->find_module_base(process_name, module_name);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error finding module base: ") + e.what());
        return 0;
    }
}

// 虚拟地址转物理地址
uint64_t VirtualToPhysical(uint64_t virtual_address) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return 0;
    }

    try {
        return g_driver->convert_virtual_to_physical(virtual_address);
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error converting address: ") + e.what());
        return 0;
    }
}

// 检查地址是否有效
BOOL IsValidAddress(uint64_t address) {
    if (!g_driver) {
        SetLastError("Driver not initialized");
        return FALSE;
    }

    try {
        // 尝试读取一个字节来检查地址是否有效
        uint8_t test_byte;
        return g_driver->read_virtual_memory(address, &test_byte, sizeof(uint8_t));
    }
    catch (const std::exception& e) {
        SetLastError(std::string("Error checking address validity: ") + e.what());
        return FALSE;
    }
}

// 获取最后的驱动错误
BOOL GetLastDriverError(char* error_buffer, uint32_t buffer_size) {
    if (!error_buffer || buffer_size == 0) {
        return FALSE;
    }

    strncpy_s(error_buffer, buffer_size, g_last_error.c_str(), _TRUNCATE);
    return TRUE;
}
	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(reinterpret_cast<void*>(virtual_address), reinterpret_cast<void*>(data), size);
	unmap_physical(mem);
	return true;
}

uintptr_t eneio_lib::convert_virtual_to_physical(uintptr_t virtual_address)
{
	uintptr_t va = virtual_address;

	unsigned short PML4 = (unsigned short)((va >> 39) & 0x1FF);
	uintptr_t PML4E = 0;
	read_physical_memory((cr3 + PML4 * sizeof(uintptr_t)), &PML4E, sizeof(PML4E));

	if (PML4E == 0)
		return 0;

	unsigned short DirectoryPtr = (unsigned short)((va >> 30) & 0x1FF);
	uintptr_t PDPTE = 0;
	read_physical_memory(((PML4E & 0xFFFFFFFFFF000) + DirectoryPtr * sizeof(uintptr_t)), &PDPTE, sizeof(PDPTE));

	if (PDPTE == 0)
		return 0;

	if ((PDPTE & (1 << 7)) != 0)
		return (PDPTE & 0xFFFFFC0000000) + (va & 0x3FFFFFFF);

	unsigned short Directory = (unsigned short)((va >> 21) & 0x1FF);

	uintptr_t PDE = 0;
	read_physical_memory(((PDPTE & 0xFFFFFFFFFF000) + Directory * sizeof(uintptr_t)), &PDE, sizeof(PDE));

	if (PDE == 0)
		return 0;

	if ((PDE & (1 << 7)) != 0)
	{
		return (PDE & 0xFFFFFFFE00000) + (va & 0x1FFFFF);
	}

	unsigned short Table = (unsigned short)((va >> 12) & 0x1FF);
	uintptr_t PTE = 0;

	read_physical_memory(((PDE & 0xFFFFFFFFFF000) + Table * sizeof(uintptr_t)), &PTE, sizeof(PTE));

	if (PTE == 0)
		return 0;

	return (PTE & 0xFFFFFFFFFF000) + (va & 0xFFF);
}

bool eneio_lib::read_virtual_memory(uintptr_t address, LPVOID output, unsigned long size)
{
	if (!address)
		return false;

	if (!size)
		return false;

	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	bool result = read_physical_memory(physical_address, output, size);
	
	return result;
}

bool eneio_lib::write_virtual_memory(uintptr_t address, LPVOID data, unsigned long size)
{
	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	write_physical_memory(physical_address, data, size);
	return true;
}

uintptr_t eneio_lib::get_process_peb(const char* process_name)
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial)
		return NULL;

	const unsigned long limit = 400;
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		char name[16] = { };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		if (strstr(process_name, name) && process_id == get_process_id(process_name))
		{
			// 获取 PEB 地址 (EPROCESS + 0x550 在大多数版本中)
			uintptr_t peb_address = 0;
			read_virtual_memory(kprocess + 0x550, &peb_address, sizeof(peb_address));
			
			uintptr_t directory_table = read_virtual_memory<uintptr_t>(kprocess + EP_DIRECTORYTABLE);
			cr3 = directory_table;
			attached_proc = process_id;
			
			return peb_address;
		}
	}
	
	return NULL;
}

void eneio_lib::enumerate_process_modules(const char* process_name)
{
	printf("[*] Enumerating modules for process: %s\n", process_name);
	
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		printf("[-] Failed to get PEB for process %s\n", process_name);
		return;
	}
	
	printf("[+] PEB address: 0x%llx\n", peb);
	
	// PEB + 0x18 = PEB_LDR_DATA
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		printf("[-] Failed to read LDR data pointer\n");
		return;
	}
	
	printf("[+] LDR data address: 0x%llx\n", ldr_data_ptr);
	
	// PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		printf("[-] Failed to read module list\n");
		return;
	}
	
	printf("[+] Module list head: 0x%llx\n", module_list);
	
	uintptr_t current_entry = module_list;
	int module_count = 0;
	
	printf("[*] Starting module enumeration...\n");
	
	do {
		if (module_count > 100) break; // 防止无限循环
		
		// 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
		// InMemoryOrderLinks 在 LDR_DATA_TABLE_ENTRY + 0x10
		uintptr_t ldr_entry = current_entry - 0x10;
		
		// LDR_DATA_TABLE_ENTRY 结构
		uintptr_t dll_base = 0;
		uintptr_t size_of_image = 0;
		
		// DllBase 在偏移 0x30
		if (!read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
			break;
		}
		
		// SizeOfImage 在偏移 0x40  
		if (!read_virtual_memory(ldr_entry + 0x40, &size_of_image, sizeof(size_of_image))) {
			break;
		}
		
		// BaseDllName 在偏移 0x58 (UNICODE_STRING)
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, module_name, min(unicode_string.Length, sizeof(module_name) - 2))) {
					// 只显示前10个模块的详细信息，避免输出过多
					if (module_count < 10) {
						printf("[+] Module %d:\n", module_count);
						printf("    Name: %ws\n", module_name);
						printf("    Base: 0x%llx\n", dll_base);
						printf("    Size: 0x%x\n", (unsigned int)size_of_image);
						printf("\n");
					} else if (module_count == 10) {
						printf("[*] ... (showing only first 10 modules)\n");
					}
				}
			}
		}
		
		// 获取下一个条目
		if (!read_virtual_memory(current_entry, &current_entry, sizeof(current_entry))) {
			break;
		}
		
		module_count++;
		
	} while (current_entry != module_list && current_entry != 0);
	
	printf("[+] Total modules found: %d\n", module_count);
}

void eneio_lib::simple_module_test(const char* process_name)
{
	printf("[*] Simple module test for: %s\n", process_name);
	
	// 首先获取进程基址
	uintptr_t base = get_process_base(process_name);
	if (!base) {
		printf("[-] Failed to get process base\n");
		return;
	}
	
	printf("[+] Process base: 0x%llx\n", base);
	
	// 尝试读取PE头
	UINT8 pe_header[64] = { 0 };
	if (read_virtual_memory(base, pe_header, 64)) {
		printf("[+] Successfully read PE header:\n");
		printf("    ");
		for (int i = 0; i < 16; i++) {
			printf("%02X ", pe_header[i]);
		}
		printf("\n");
		
		if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) {
			printf("[+] Valid PE signature found!\n");
		}
	} else {
		printf("[-] Failed to read PE header\n");
	}
}

void eneio_lib::list_running_processes()
{
	printf("[*] Listing running processes...\n");
	
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[-] Failed to get system directory base\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[-] Failed to get initial kprocess\n");
		return;
	}

	printf("\n%-6s %-20s %-16s\n", "PID", "Process Name", "Base Address");
	printf("%-6s %-20s %-16s\n", "------", "--------------------", "----------------");

	// 简化的遍历方法 - 回到原始逻辑但加入重复检测
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	int process_count = 0;
	std::vector<int> seen_pids; // 只检测PID重复
	
	// 读取第一个flink
	read_virtual_memory(flink, &flink, sizeof(PVOID));
	
	do {
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		// 读取进程信息
		char name[16] = { 0 };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));
		
		uintptr_t base_address = 0;
		read_virtual_memory(kprocess + EP_SECTIONBASE, &base_address, sizeof(base_address));
		
		// 验证进程信息并检查重复
		if (process_id > 0 && process_id < 100000 && strlen(name) > 0) {
			bool already_seen = false;
			for (int pid : seen_pids) {
				if (pid == process_id) {
					already_seen = true;
					break;
				}
			}
			
			if (!already_seen) {
				printf("%-6d %-20s 0x%llx\n", process_id, name, base_address);
				seen_pids.push_back(process_id);
				process_count++;
			}
		}
		
		// 获取下一个链表项
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		
		// 安全检查：如果进程数量过多，可能有问题
		if (process_count > 500) {
			printf("[*] Too many processes, stopping enumeration\n");
			break;
		}
		
	} while (flink != link_start && flink != 0);
	
	printf("\n[+] Total unique processes found: %d\n", process_count);
}

void eneio_lib::search_processes_by_keyword(const char* keyword)
{
	printf("[*] Searching for processes containing keyword: %s\n", keyword);
	
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3) {
		printf("[-] Failed to get system directory base\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial) {
		printf("[-] Failed to get initial kprocess\n");
		return;
	}

	const unsigned long limit = 800;
	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	int found_count = 0;

	printf("\n%-6s %-30s %-16s\n", "PID", "Process Name", "Base Address");
	printf("%-6s %-30s %-16s\n", "------", "------------------------------", "----------------");

	std::vector<int> seen_pids; // 避免重复
	
	for (int a = 0; a < limit; a++)
	{
		if (a > 0 && flink == link_start) break;
		
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		if (flink == 0) break;
		
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		
		char name[16] = { 0 };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));
		
		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));
		
		// 检查重复
		bool already_seen = false;
		for (int pid : seen_pids) {
			if (pid == process_id) {
				already_seen = true;
				break;
			}
		}
		if (already_seen) break;
		
		uintptr_t base_address = 0;
		read_virtual_memory(kprocess + EP_SECTIONBASE, &base_address, sizeof(base_address));

		// 检查进程名是否包含关键词（不区分大小写）
		if (process_id > 0 && strlen(name) > 0) {
			seen_pids.push_back(process_id);
			
			char lower_name[16] = { 0 };
			char lower_keyword[256] = { 0 };
			
			// 转换为小写进行比较
			for (int i = 0; i < strlen(name); i++) {
				lower_name[i] = tolower(name[i]);
			}
			for (int i = 0; i < strlen(keyword); i++) {
				lower_keyword[i] = tolower(keyword[i]);
			}
			
			if (strstr(lower_name, lower_keyword) != NULL) {
				printf("%-6d %-30s 0x%llx\n", process_id, name, base_address);
				found_count++;
			}
		}
	}
	
	printf("\n[+] Found %d processes containing '%s'\n", found_count, keyword);
}

uintptr_t eneio_lib::find_module_base(const char* process_name, const wchar_t* module_name)
{
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		return 0;
	}
	
	// PEB + 0x18 = PEB_LDR_DATA
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		return 0;
	}
	
	// PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		return 0;
	}
	
	uintptr_t current_entry = module_list;
	int module_count = 0;
	
	do {
		if (module_count > 100) break;
		
		// 从链表条目中减去偏移得到实际的LDR_DATA_TABLE_ENTRY
		uintptr_t ldr_entry = current_entry - 0x10;
		
		// DllBase 在偏移 0x30
		uintptr_t dll_base = 0;
		if (!read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
			break;
		}
		
		// BaseDllName 在偏移 0x58 (UNICODE_STRING)
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t current_module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, current_module_name, min(unicode_string.Length, sizeof(current_module_name) - 2))) {
					// 比较模块名称（不区分大小写）
					if (wcscmp(current_module_name, module_name) == 0 || 
						_wcsicmp(current_module_name, module_name) == 0) {
						return dll_base;
					}
				}
			}
		}
		
		// 获取下一个条目
		if (!read_virtual_memory(current_entry, &current_entry, sizeof(current_entry))) {
			break;
		}
		
		module_count++;
		
	} while (current_entry != module_list && current_entry != 0);
	
	return 0; // 未找到
}

void eneio_lib::test_simple_module_read(const char* process_name)
{
	printf("[*] Testing simple module read for: %s\n", process_name);
	
	uintptr_t peb = get_process_peb(process_name);
	if (!peb) {
		printf("[-] Failed to get PEB\n");
		return;
	}
	
	printf("[+] PEB: 0x%llx\n", peb);
	
	// 读取 LDR 指针
	uintptr_t ldr_data_ptr = 0;
	if (!read_virtual_memory(peb + 0x18, &ldr_data_ptr, sizeof(ldr_data_ptr))) {
		printf("[-] Failed to read LDR pointer\n");
		return;
	}
	
	printf("[+] LDR: 0x%llx\n", ldr_data_ptr);
	
	// 读取模块列表头
	uintptr_t module_list = 0;
	if (!read_virtual_memory(ldr_data_ptr + 0x20, &module_list, sizeof(module_list))) {
		printf("[-] Failed to read module list\n");
		return;
	}
	
	printf("[+] Module list: 0x%llx\n", module_list);
	
	// 尝试读取第一个模块的基址
	// 需要从链表条目减去0x10偏移得到LDR_DATA_TABLE_ENTRY
	uintptr_t ldr_entry = module_list - 0x10;
	uintptr_t dll_base = 0;
	if (read_virtual_memory(ldr_entry + 0x30, &dll_base, sizeof(dll_base))) {
		printf("[+] First module base (corrected): 0x%llx\n", dll_base);
		
		// 尝试读取模块名称
		struct {
			USHORT Length;
			USHORT MaximumLength;
			uintptr_t Buffer;
		} unicode_string;
		
		if (read_virtual_memory(ldr_entry + 0x58, &unicode_string, sizeof(unicode_string))) {
			wchar_t module_name[256] = { 0 };
			if (unicode_string.Buffer && unicode_string.Length > 0 && unicode_string.Length < 512) {
				if (read_virtual_memory(unicode_string.Buffer, module_name, min(unicode_string.Length, sizeof(module_name) - 2))) {
					printf("[+] First module name: %ws\n", module_name);
				}
			}
		}
	} else {
		printf("[-] Failed to read first module base\n");
	}
}