#!/usr/bin/env python3
"""
快速测试脚本 - 自动启动notepad并测试DLL功能
"""

import ctypes
from ctypes import wintypes, Structure, c_uint8, c_uint16, c_uint32, c_uint64, c_char
import subprocess
import time
import sys

# 进程信息结构
class ProcessInfo(Structure):
    _fields_ = [
        ("process_id", c_uint32),
        ("process_name", c_char * 256),
        ("base_address", c_uint64),
        ("cr3", c_uint64)
    ]

def load_dll():
    """加载DLL"""
    possible_paths = [
        "x64\\Debug\\wnbios_poc.dll",
        "x64\\Release\\wnbios_poc.dll",
        "wnbios_poc.dll"
    ]
    
    for path in possible_paths:
        try:
            dll = ctypes.CDLL(path)
            print(f"[+] Loaded DLL from: {path}")
            return dll
        except Exception as e:
            continue
    
    raise Exception("Could not find DLL")

def setup_dll_functions(dll):
    """设置DLL函数"""
    # 初始化函数
    dll.InitializeDriver.restype = wintypes.BOOL
    dll.CleanupDriver.restype = None
    
    # 进程函数
    dll.GetProcessInfo.argtypes = [ctypes.c_char_p, ctypes.POINTER(ProcessInfo)]
    dll.GetProcessInfo.restype = wintypes.BOOL
    
    # 内存读取函数
    dll.ReadMemoryBytes.argtypes = [c_uint64, ctypes.POINTER(c_uint8), c_uint32]
    dll.ReadMemoryBytes.restype = wintypes.BOOL
    
    dll.ReadMemoryDword.argtypes = [c_uint64, ctypes.POINTER(c_uint32)]
    dll.ReadMemoryDword.restype = wintypes.BOOL
    
    # 错误函数
    dll.GetLastDriverError.argtypes = [ctypes.c_char_p, c_uint32]
    dll.GetLastDriverError.restype = wintypes.BOOL

def get_last_error(dll):
    """获取错误信息"""
    error_buffer = ctypes.create_string_buffer(256)
    if dll.GetLastDriverError(error_buffer, 256):
        return error_buffer.value.decode('utf-8')
    return "Unknown error"

def start_notepad():
    """启动记事本"""
    try:
        proc = subprocess.Popen(["notepad.exe"])
        print(f"[+] Started notepad.exe (PID: {proc.pid})")
        time.sleep(2)  # 等待启动
        return True
    except Exception as e:
        print(f"[-] Failed to start notepad: {e}")
        return False

def main():
    print("[*] Quick Test - WnBios POC DLL")
    print("[*] This will automatically start notepad and test the DLL")
    print("=" * 60)
    
    try:
        # 加载DLL
        print("[1] Loading DLL...")
        dll = load_dll()
        setup_dll_functions(dll)
        
        # 初始化驱动
        print("[2] Initializing driver...")
        if not dll.InitializeDriver():
            error = get_last_error(dll)
            raise Exception(f"Driver initialization failed: {error}")
        print("[+] Driver initialized successfully!")
        
        # 启动notepad
        print("[3] Starting notepad...")
        if not start_notepad():
            raise Exception("Failed to start notepad")
        
        # 获取进程信息
        print("[4] Getting process information...")
        proc_info = ProcessInfo()
        if not dll.GetProcessInfo(b"notepad.exe", ctypes.byref(proc_info)):
            error = get_last_error(dll)
            raise Exception(f"Failed to get process info: {error}")
        
        print(f"[+] Process found:")
        print(f"    Name: {proc_info.process_name.decode('utf-8')}")
        print(f"    PID: {proc_info.process_id}")
        print(f"    Base: 0x{proc_info.base_address:016X}")
        print(f"    CR3: 0x{proc_info.cr3:016X}")
        
        # 测试内存读取
        print("[5] Testing memory read...")
        base_addr = proc_info.base_address
        
        # 读取PE头
        pe_header = (c_uint8 * 16)()
        if dll.ReadMemoryBytes(base_addr, pe_header, 16):
            header_bytes = bytes(pe_header)
            print(f"[+] PE Header: {header_bytes.hex().upper()}")
            
            if header_bytes[0:2] == b'MZ':
                print(f"[+] Valid PE signature found!")
            else:
                print(f"[-] Invalid PE signature")
        else:
            error = get_last_error(dll)
            print(f"[-] Failed to read PE header: {error}")
        
        # 读取一个DWORD
        dword_val = c_uint32()
        if dll.ReadMemoryDword(base_addr, ctypes.byref(dword_val)):
            print(f"[+] DWORD at base: 0x{dword_val.value:08X}")
        else:
            error = get_last_error(dll)
            print(f"[-] Failed to read DWORD: {error}")
        
        print("\n[+] Test completed successfully!")
        print("[*] You can now use the DLL for your own applications")
        
    except Exception as e:
        print(f"[-] Test failed: {e}")
        return 1
    
    finally:
        # 清理
        print("\n[*] Cleaning up...")
        try:
            if 'dll' in locals():
                dll.CleanupDriver()
            print("[+] Cleanup completed")
        except:
            pass
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
